# 🐛 Room Visibility Debugging Guide

## Issue: Rooms not visible during booking in production

### ✅ Fixed Issues

1. **Bulk Room Creation Missing `visibleTo` Field**
   - **Problem**: `addRoomsInBulk` mutation wasn't including `visibleTo` field
   - **Fix**: Updated `convex/hotels.ts` line 101 to include `visibleTo: args.visibleTo`
   - **Impact**: Bulk-created rooms now properly inherit visibility settings

### 🔍 Debugging Steps

#### 1. Add Temporary Debug Component
Add `RoomVisibilityDebugger` to your event registration flow:

```tsx
import { RoomVisibilityDebugger } from "./RoomVisibilityDebugger";

// In your EventModal component:
<RoomVisibilityDebugger eventId={event._id} attendeeType={attendeeType} />
```

#### 2. Check Room Visibility Settings
The debugger will show:
- All rooms for a hotel
- Their `visibleTo` settings
- Whether they're visible to the current attendee type
- Availability status

#### 3. Common Issues & Solutions

**Issue A: Rooms have restrictive `visibleTo` settings**
```
Room: 101 | Visible To: VIP, Delegates | Visible to Guests: ❌ NO
```
**Solution**: Update room visibility or check attendee type assignment

**Issue B: Rooms created without `visibleTo` field**
```
Room: 102 | Visible To: undefined | Visible to Guests: ✅ YES
```
**Expected**: Should show "All attendee types (no restrictions)"

**Issue C: AttendeeType not being passed correctly**
```
Current Attendee Type: undefined
```
**Solution**: Check event registration flow passes attendeeType

### 🔧 Backend Debugging Queries

#### Debug Room Visibility
```typescript
// Use this query in your browser console:
const debugData = await convex.query(api.hotels.debugRoomVisibility, {
  hotelId: "your-hotel-id",
  attendeeType: "Guests"
});
console.log(debugData);
```

#### Check All Rooms for Event
```typescript
// Get all hotels for event
const hotels = await convex.query(api.hotels.getAccommodationsByEvent, {
  eventId: "your-event-id"
});

// For each hotel, check rooms
for (const hotel of hotels) {
  const rooms = await convex.query(api.hotels.debugRoomVisibility, {
    hotelId: hotel._id,
    attendeeType: "Guests" // or whatever type you're testing
  });
  console.log(`Hotel: ${hotel.name}`, rooms);
}
```

### 📊 Room Visibility Logic

Rooms are visible when:
1. `visibleTo` is `undefined` or empty array → Visible to ALL
2. `visibleTo` contains the user's attendeeType → Visible to SPECIFIC types
3. Room has available capacity → Not fully booked

### 🚨 Production Debugging Checklist

1. **Check attendeeType is being passed**
   - Look in browser dev tools network tab
   - Verify `getAvailableRoomsByHotel` query includes attendeeType

2. **Verify room creation process**
   - Check if rooms were created via bulk creation before the fix
   - Look for rooms with missing `visibleTo` field

3. **Test with different attendee types**
   - Try registering as different attendee types
   - Check if rooms appear/disappear based on type

4. **Check room availability**
   - Rooms with 0 available capacity won't show
   - Verify booking counts are correct

### 🔄 Quick Fixes

#### Fix 1: Reset Room Visibility (if needed)
```typescript
// If you need to reset all rooms to be visible to everyone:
// WARNING: Only run this if you're sure about the impact
const updateRoomVisibility = useMutation(api.hotels.updateRoomVisibility);

// For each room that should be visible to all:
await updateRoomVisibility({
  roomId: "room-id",
  visibleTo: [] // Empty array = visible to all
});
```

#### Fix 2: Bulk Update Room Visibility
Create a temporary mutation to fix existing rooms:

```typescript
// Add to convex/hotels.ts temporarily:
export const fixBulkRoomVisibility = mutation({
  args: { hotelId: v.id("hotels") },
  handler: async (ctx, args) => {
    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_hotelId", (q) => q.eq("hotelId", args.hotelId))
      .collect();
    
    let updated = 0;
    for (const room of rooms) {
      if (room.visibleTo === undefined) {
        await ctx.db.patch(room._id, { visibleTo: [] });
        updated++;
      }
    }
    return { message: `Updated ${updated} rooms` };
  },
});
```

### 🧹 Cleanup

After debugging, remember to:
1. Remove `RoomVisibilityDebugger` from production code
2. Remove debug queries from backend
3. Remove temporary fix mutations

### 📞 Support

If rooms are still not visible after these steps:
1. Check browser console for errors
2. Verify database indexes are working
3. Check if there are any caching issues
4. Verify user permissions and authentication
