{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "build": "npx convex deploy && vite build", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@convex-dev/auth": "^0.0.80", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.2", "date-fns": "^4.1.0", "lucide-react": "^0.514.0", "papaparse": "^5.5.3", "paystack-js": "^0.18.1", "postcss": "^8.5.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/papaparse": "^5.3.14", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}