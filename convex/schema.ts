import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// Enum-like structures for validation from your specifications
export const eventLevels = v.union(
  v.literal("National"),
  v.literal("Grand"),
  v.literal("District"),
  v.literal("Local")
);

export const allowedAttendeeTypes = v.union( // As per "Event Attendees {checkbox}"
  v.literal("VIP"),
  v.literal("Delegates"), // Note: "delegates" in spec, using "Delegates" for consistency
  v.literal("Observers"),
  v.literal("Guests")
);

export const userTitles = v.union(
  v.literal("Mr."), v.literal("Mrs."), v.literal("Ms."), v.literal("Dr."), v.literal("Prof."),
  v.literal("Ing."), v.literal("Hon."), v.literal("Rev."), v.literal("Ps."), v.literal("Aps."),
  v.literal("ESQ."), v.literal("Suv."), v.literal("Chief"), v.literal("Nana")
);

export const maritalStatuses = v.union(
  v.literal("Single"), v.literal("Married"), v.literal("Divorced"), v.literal("Separated"),
  v.literal("Widowed"), v.literal("Other")
);

export const employmentStatuses = v.union(
  v.literal("Employed"), v.literal("Self-Employed"), v.literal("Unemployed"),
  v.literal("Student"), v.literal("Other")
);

export const ksjiRanks = v.union( // Add all ranks from your spec (*3)
  v.literal("1st Lieutenant (1Lt.)"),
  v.literal("2nd Lieutenant (2Lt)"),
  v.literal("Captain (Capt.)"),
  v.literal("Major (Maj.)"),
  v.literal("Lieutenant Colonel (Lt. Col)"),
  v.literal("Colonel (Col)"),
  v.literal("Brigadier General (BGen.)"),
  v.literal("Major General (MGen.)"),
  v.literal("Lieutenant General (Lt. Gen.)"),
  v.literal("General"),
  v.literal("Noble Sister (NS)")
);

export const paymentStatuses = v.union(
  v.literal("pending"),
  v.literal("success"),
  v.literal("failed"),
  v.literal("abandoned"),
  v.literal("waived"),
  v.literal("free")
);

export const genders = v.union(
  v.literal("Male"), v.literal("Female"),
);

export const educationalBackgrounds = v.union(
  v.literal("Primary"), v.literal("Secondary"), v.literal("Tertiary"), v.literal("Vocational"), v.literal("Post-Tertiary"), v.literal("Other")
);


// Note: For KSJI Offices (*5), a v.union would be extremely long.
// Consider a separate table `ksjiOffices` if offices are dynamic or very numerous.
// For now, assuming it's a string field, but a structured approach is better long-term.

const applicationTables = {
  users: defineTable({
    tokenIdentifier: v.optional(v.string()),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    // role: v.optional(v.union(v.literal("admin"), v.literal("member"))),
    // KSJI Portal Specific Roles & Data
    isEventOrganizerGlobal: v.optional(v.boolean()), // Can create/manage any event
    managesEvents: v.optional(v.array(v.id("events"))), // For specific event organizer roles
    isStaffForEvents: v.optional(v.array(v.id("events"))), // For specific event staff roles
    systemRole: v.optional(v.string()), // e.g., "member", "admin" - can be used for initial setup
    onboardingCompleted: v.optional(v.boolean()),

    // Personal Information from UserProfile
    title: v.optional(userTitles),
    gender: v.optional(genders), // e.g., "Male", "Female", "Other"
    dateOfBirth: v.optional(v.string()), // Store as ISO string or number (timestamp)
    maritalStatus: v.optional(maritalStatuses),
    otherMaritalStatusDetails: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    memberNumber: v.optional(v.string()),
    organizationName: v.optional(v.string()),
    organizationId: v.optional(v.id("organizations")),

    // Order Information
    yearOfInitiation: v.optional(v.number()),
    rank: v.optional(ksjiRanks),
    commenderyId: v.optional(v.id("commanderies")),
    commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
    districtId: v.optional(v.id("districts")),
    grandId: v.optional(v.id("grands")),
    officeId: v.optional(v.id("ksjiOffices")),
    currentOffice: v.optional(v.string()), // Consider a structured list or separate table if complex
    officesHeld: v.optional(v.array(v.object({ officeId: v.id("ksjiOffices"), year: v.number() }))), // For history
    // officesHeld: v.optional(v.array(v.object({ office: v.string(), year: v.number() }))), // For history

    // Education & Employment Info
    educationalBackground: v.optional(v.string()),
    otherEducationalBackgroundDetails: v.optional(v.string()),
    employmentStatus: v.optional(employmentStatuses),
    otherEmploymentStatusDetails: v.optional(v.string()),
    careerProfession: v.optional(v.string()),
    currentPlaceOfWork: v.optional(v.string()),
    otherSkillsExpertise: v.optional(v.string()),
    passwordSet: v.optional(v.boolean()),
    defaultAttendeeType: v.optional(allowedAttendeeTypes),

  })
    .index("by_tokenIdentifier", ["tokenIdentifier"])
    .index("by_email", ["email"])
    .index("by_phoneNumber", ["phoneNumber"])
    .index("by_name", ["name"])
    .searchIndex("search_users", {
      searchField: "name",
      filterFields: ["email"],
    }),
  preRegisteredUsers: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    title: v.optional(userTitles),
    gender: v.optional(genders),
    phoneNumber: v.optional(v.string()),
    memberNumber: v.optional(v.string()),
    organizationName: v.optional(v.string()),
    dateOfBirth: v.optional(v.string()),
    maritalStatus: v.optional(maritalStatuses),
    yearOfInitiation: v.optional(v.number()),
    rank: v.optional(ksjiRanks),
    grandId: v.optional(v.id("grands")),
    districtId: v.optional(v.id("districts")),
    commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
    officeId: v.optional(v.id("ksjiOffices")),
    otherMaritalStatusDetails: v.optional(v.string()),
    educationalBackground: v.optional(v.string()),
    otherEducationalBackgroundDetails: v.optional(v.string()),
    employmentStatus: v.optional(employmentStatuses),
    otherEmploymentStatusDetails: v.optional(v.string()),
    careerProfession: v.optional(v.string()),
    currentPlaceOfWork: v.optional(v.string()),
    otherSkillsExpertise: v.optional(v.string()),
  }).index("by_email", ["email"]),
  events: defineTable({
    title: v.string(),
    description: v.string(),
    location: v.string(),
    imageUrl: v.optional(v.string()),

    startDate: v.optional(v.number()), // Timestamp (Event Date start)
    endDate: v.optional(v.number()),   // Timestamp (Event Date end)
    startTime: v.optional(v.string()), // HH:MM (Event Time start)
    endTime: v.optional(v.string()),   // HH:MM (Event Time end)
    price: v.number(),      // Event cost (price or 0 for free)
    level: v.optional(eventLevels),    // Event level {radio}
    allowedAttendeeTypes: v.optional(v.array(allowedAttendeeTypes)), // Event Attendees {checkbox}
    featuredImageStorageId: v.optional(v.id("_storage")), // Event Featured Image {media}
    organizerId: v.id("users"), // Event Organizer {User}
    organizerName: v.optional(v.string()), // Denormalized for easier display
    registrationOpenDate: v.optional(v.string()), // Timestamp
    registrationCloseDate: v.optional(v.string()), // Timestamp
    exemptedEmails: v.optional(v.array(v.string())), // Exempted Emails {checkbox}

    // Optional fields from previous schema or new requirements
    totalTickets: v.optional(v.number()), // Max capacity, if applicable
    soldTickets: v.optional(v.number()),  // Tickets sold so far, default 0
    category: v.optional(v.string()), // If you still want to use categories for filtering
  })
    .index("by_date", ["startDate"])
    .index("by_organizer", ["organizerId"])
    .index("by_category", ["category"]),

  // Track attendance by date for each ticket
  attendanceRecords: defineTable({
    ticketId: v.id("tickets"),
    eventId: v.id("events"),
    date: v.string(), // YYYY-MM-DD format
    isPresent: v.boolean(),
    recordedAt: v.number(),
    recordedBy: v.id("users"),
  })
    .index("by_ticket_date", ["ticketId", "date"])
    .index("by_event_date", ["eventId", "date"]),

  // Track ticket information
  tickets: defineTable({
    attendedDates: v.optional(v.array(v.string())), // Array of dates in YYYY-MM-DD format
    userId: v.id("users"),
    eventId: v.id("events"), // Ensures the event exists
    attendeeType: v.optional(allowedAttendeeTypes), // VIP, Delegate, Observer, Guest
    delegateLevel: v.optional(eventLevels), // Delegate level {radio}
    delegateOffice: v.optional(v.string()),
    delegateEndorser: v.optional(v.string()), // Grand Pres, Grand Sec, etc.
    isEndorsed: v.optional(v.boolean()), // For staff endorsement
    endorsedBy: v.optional(v.id("users")),

    isExemptFromPayment: v.optional(v.boolean()), // From Exemption List
    isAttended: v.optional(v.boolean()), // For Event Attendance marking

    // Original ticket fields
    quantity: v.number(),
    totalPrice: v.number(),
    purchaseDate: v.number(),
    status: v.union(v.literal("confirmed"), v.literal("cancelled"), v.literal("pending")),
    paymentReference: v.optional(v.string()),
    paymentStatus: paymentStatuses,
    roomId: v.optional(v.id("rooms")),
  })
    .index("by_userId_eventId", ["userId", "eventId"])
    .index("by_userId", ["userId"])
    .index("by_eventId", ["eventId"])
    .index("by_payment_reference", ["paymentReference"])
    .index("by_roomId", ["roomId"]),

  userRoles: defineTable({
    userId: v.id("users"),
    role: v.union(v.literal("admin"), v.literal("member")),
  })
    .index("by_user", ["userId"]),

  eventAnalytics: defineTable({
    eventId: v.id("events"),
    date: v.number(), // Daily analytics
    registrations: v.number(),
    revenue: v.number(),
    cancellations: v.number(),
    views: v.number(),
  })
    .index("by_eventId", ["eventId"]),

  // KSJI Hierarchy Tables (from your specification)
  grands: defineTable({ name: v.string() }).index("by_name", ["name"]),

  districts: defineTable({
    name: v.string(),
    grandId: v.id("grands"),
  }).index("by_grandId", ["grandId"]),

  commanderiesAuxiliaries: defineTable({
    name: v.string(),
    districtId: v.id("districts"),
    type: v.optional(v.union(v.literal("Commandery"), v.literal("Auxiliary"))), // To distinguish
  }).index("by_districtId", ["districtId"]),

  commanderies: defineTable({
    name: v.string(),
    districtId: v.id("districts"),
  }).index("by_districtId", ["districtId"]),

  hotels: defineTable({
    name: v.string(),
    location: v.string(),
    eventId: v.id("events"),
    // Add other hotel details like contact, address, etc.
  }).index("by_eventId", ["eventId"]),

  rooms: defineTable({
    hotelId: v.id("hotels"),
    roomNumber: v.string(),
    capacity: v.number(), // e.g., 2 for a 2-person room
    pricePerBedPerDay: v.number(),
    visibleTo: v.optional(v.array(allowedAttendeeTypes)), // For selected member categories
    // You can add more details like room type (e.g., "Standard", "Deluxe")
  })
    .index("by_hotelId", ["hotelId"])
    .index("by_hotel_and_number", ["hotelId", "roomNumber"]),

  bookings: defineTable({
    userId: v.id("users"),
    roomId: v.id("rooms"),
    bedNumber: v.number(), // e.g., 1, 2, 3...
    ticketId: v.id("tickets"),
    status: v.union(v.literal("confirmed"), v.literal("cancelled")),
  })
    .index("by_roomId", ["roomId"])
    .index("by_ticketId", ["ticketId"])
    .index("by_userId", ["userId"]), // Added index for querying by userId

  // Separate table for room-only transactions (add-on bookings)
  roomTransactions: defineTable({
    userId: v.id("users"),
    eventId: v.id("events"),
    originalTicketId: v.id("tickets"), // Reference to the original event ticket
    roomId: v.id("rooms"),
    totalPrice: v.number(), // Room price + transaction charges
    purchaseDate: v.number(),
    status: v.union(v.literal("confirmed"), v.literal("cancelled"), v.literal("pending")),
    paymentReference: v.optional(v.string()),
    paymentStatus: paymentStatuses,
  })
    .index("by_userId", ["userId"])
    .index("by_eventId", ["eventId"])
    .index("by_originalTicketId", ["originalTicketId"]),

  ksjiOffices: defineTable({
    name: v.string(),
    type: v.union(v.literal("Knights"), v.literal("Ladies' Auxiliary")),
  }).index("by_name", ["name"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
