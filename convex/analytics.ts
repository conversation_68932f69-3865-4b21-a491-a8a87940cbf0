import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId, getUser } from "./auth";

export const getEventInsights = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const event = await ctx.db.get(args.eventId);
    if (!event || event.organizerId !== userId) {
      throw new Error("Unauthorized");
    }
    const totalTickets = event.totalTickets ?? 0;
    const soldTickets = event.soldTickets ?? 0;
    const availableTickets = totalTickets - soldTickets;

    // Check if user is admin
    const user = await getUser(ctx, userId);
    if (!user) return null;

    if (!user.isEventOrganizerGlobal) {
      throw new Error("Only event organizers can view event insights");
    }

    // Get all tickets for this event
    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();

    const confirmedTickets = tickets.filter((t) => t.status === "confirmed" && t.paymentStatus === "success");
    const cancelledTickets = tickets.filter((t) => t.status === "cancelled");
    const pendingTickets = tickets.filter((t) => t.paymentStatus === "pending");

    // Calculate metrics
    const totalRevenue = confirmedTickets.reduce((sum, ticket) => sum + ticket.totalPrice, 0);
    const totalAttendees = confirmedTickets.reduce((sum, ticket) => sum + ticket.quantity, 0);
    const averageTicketPrice = totalAttendees > 0 ? totalRevenue / totalAttendees : 0;

    // Registration timeline (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentTickets = confirmedTickets.filter(t => t.purchaseDate >= thirtyDaysAgo);
    
    // Group by day
    const dailyRegistrations = new Map<string, number>();
    const dailyRevenue = new Map<string, number>();
    
    recentTickets.forEach(ticket => {
      const date = new Date(ticket.purchaseDate).toISOString().split('T')[0];
      dailyRegistrations.set(date, (dailyRegistrations.get(date) || 0) + ticket.quantity);
      dailyRevenue.set(date, (dailyRevenue.get(date) || 0) + ticket.totalPrice);
    });

    // Convert to arrays for charting
    const registrationTimeline = Array.from(dailyRegistrations.entries())
      .map(([date, count]) => ({ date, registrations: count }))
      .sort((a, b) => a.date.localeCompare(b.date));

    const revenueTimeline = Array.from(dailyRevenue.entries())
      .map(([date, revenue]) => ({ date, revenue }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // Get user details for attendees
    const attendeeDetails = await Promise.all(
      confirmedTickets.map(async (ticket) => {
        const user = await ctx.db.get(ticket.userId);
        const grand = user?.grandId ? await ctx.db.get(user.grandId) : null;
        const district = user?.districtId ? await ctx.db.get(user.districtId) : null;
        const commandery = user?.commanderyAuxiliaryId ? await ctx.db.get(user.commanderyAuxiliaryId) : null;
        return {
          ...ticket,
          userName: user?.name || "Unknown",
          userEmail: user?.email || "Unknown",
          gender: user?.gender,
          rank: user?.rank,
          grandName: grand?.name,
          districtName: district?.name,
          commanderyAuxiliaryName: commandery?.name,
        };
      })
    );

    return {
      event,
      summary: {
        totalRevenue,
        totalAttendees,
        confirmedTickets: confirmedTickets.length,
        cancelledTickets: cancelledTickets.length,
        pendingTickets: pendingTickets.length,
        averageTicketPrice,
        occupancyRate: (totalAttendees / totalTickets) * 100,
        availableTickets,
      },
      timeline: {
        registrations: registrationTimeline,
        revenue: revenueTimeline,
      },
      attendees: attendeeDetails,
    };
  },
});

export const recordEventView = mutation({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    // Check if analytics record exists for today
    const existingAnalytics = await ctx.db
      .query("eventAnalytics")
      .withIndex("by_eventId", (q) => 
        q.eq("eventId", args.eventId).eq("_creationTime", todayTimestamp)
      )
      .first();

    if (existingAnalytics) {
      await ctx.db.patch(existingAnalytics._id, {
        views: existingAnalytics.views + 1,
      });
    } else {
      await ctx.db.insert("eventAnalytics", {
        eventId: args.eventId,
        date: todayTimestamp,
        registrations: 0,
        revenue: 0,
        cancellations: 0,
        views: 1,
      });
    }
  },
});
