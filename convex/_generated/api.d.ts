/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as analytics from "../analytics.js";
import type * as attendance from "../attendance.js";
import type * as auth from "../auth.js";
import type * as bookings from "../bookings.js";
import type * as data from "../data.js";
import type * as events from "../events.js";
import type * as files from "../files.js";
import type * as hotels from "../hotels.js";
import type * as http from "../http.js";
import type * as payments from "../payments.js";
import type * as router from "../router.js";
import type * as seed from "../seed.js";
import type * as tickets from "../tickets.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  analytics: typeof analytics;
  attendance: typeof attendance;
  auth: typeof auth;
  bookings: typeof bookings;
  data: typeof data;
  events: typeof events;
  files: typeof files;
  hotels: typeof hotels;
  http: typeof http;
  payments: typeof payments;
  router: typeof router;
  seed: typeof seed;
  tickets: typeof tickets;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
