import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId, getUser } from "./auth";
import { Id } from "./_generated/dataModel";
import { api } from "./_generated/api";

export const myTickets = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();

    const ticketsWithEvents = await Promise.all(
      tickets.map(async (ticket) => {
        const event = await ctx.db.get(ticket.eventId);

        // Populate room details if roomId exists
        let roomDetails = null;
        if (ticket.roomId) {
          const room = await ctx.db.get(ticket.roomId);
          if (room) {
            const hotel = await ctx.db.get(room.hotelId);
            roomDetails = {
              roomNumber: room.roomNumber,
              hotelName: hotel?.name || 'Hotel',
              type: `${hotel?.name || 'Hotel'} - Room ${room.roomNumber}`,
              price: room.pricePerBedPerDay,
              capacity: room.capacity,
            };
          }
        }

        return {
          ...ticket,
          event,
          roomDetails,
        };
      })
    );

    return ticketsWithEvents.filter(ticket => ticket.event !== null);
  },
});

export const cancel = mutation({
  args: { ticketId: v.id("tickets") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in");
    }

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) {
      throw new Error("Ticket not found");
    }

    if (ticket.userId !== userId) {
      throw new Error("Can only cancel your own tickets");
    }

    if (ticket.status === "cancelled") {
      throw new Error("Ticket already cancelled");
    }

    const event = await ctx.db.get(ticket.eventId);
    if (!event) {
      throw new Error("Event not found");
    }

    // Update ticket status
    await ctx.db.patch(args.ticketId, { status: "cancelled" });

    // Update sold tickets count only if payment was successful
    if (ticket.paymentStatus === "success") {
      await ctx.db.patch(ticket.eventId, {
        soldTickets: (event?.soldTickets || 0) - ticket.quantity,
      });
    }
  },
});

export const getEventTickets = query({
  args: {
    eventId: v.id("events"),
    date: v.optional(v.string()) // Optional date parameter to filter by specific date
  },
  handler: async (ctx, args) => {
    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();

    // Get user details for each ticket and check attendance if date is provided
    const ticketsWithUsers = await Promise.all(
      tickets.map(async (ticket) => {
        const user = await ctx.db.get(ticket.userId);
        let isPresent = false;

        if (args.date) {
          // Check if there's an attendance record for this ticket on the specified date
          const attendance = await ctx.db
            .query("attendanceRecords")
            .withIndex("by_ticket_date", (q) =>
              q.eq("ticketId", ticket._id).eq("date", args.date!)
            )
            .first();
          isPresent = attendance?.isPresent || false;
        }

        return {
          ...ticket,
          userName: user?.name || 'Unknown User',
          userEmail: user?.email || 'No email',
          isPresent,
        };
      })
    );

    return ticketsWithUsers;
  },
});

// Kept for backward compatibility.
// It's recommended to use the one in `convex/attendance.ts` directly.
export const toggleAttendance = mutation({
  args: {
    ticketId: v.id("tickets"),
    date: v.optional(v.string()),
    isPresent: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{ success: boolean }> => {
    const date = args.date || new Date().toISOString().split("T")[0];
    const isPresent = args.isPresent ?? true;

    // Call the toggleAttendance mutation in `convex/attendance.ts`
    return await ctx.runMutation(api.attendance.toggleAttendance, {
      ticketId: args.ticketId,
      date,
      isPresent,
    });
  },
});

export const getUserTickets = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Not authenticated");
    }

    // Check if current user is admin
    const adminUser = await getUser(ctx, currentUserId);

    if (!adminUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to change user roles. Requires Global Organizer role.");
    }

    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();

    return Promise.all(
      tickets.map(async (ticket) => {
        const event = await ctx.db.get(ticket.eventId);
        let roomDetails = null;

        if (ticket.roomId) {
          const room = await ctx.db.get(ticket.roomId);
          if (room) {
            const hotel = await ctx.db.get(room.hotelId);
            roomDetails = {
              hotelName: hotel?.name,
              roomNumber: room.roomNumber,
            };
          }
        }

        return {
          ...ticket,
          event: {
            title: event?.title || "Unknown Event",
            startDate: event?.startDate,
            location: event?.location,
          },
          roomDetails,
        };
      })
    );
  },
});
