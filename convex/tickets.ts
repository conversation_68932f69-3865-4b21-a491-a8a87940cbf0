import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "./auth";

export const myTickets = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();

    const ticketsWithEvents = await Promise.all(
      tickets.map(async (ticket) => {
        const event = await ctx.db.get(ticket.eventId);

        // Populate room details if roomId exists
        let roomDetails = null;
        if (ticket.roomId) {
          const room = await ctx.db.get(ticket.roomId);
          if (room) {
            const hotel = await ctx.db.get(room.hotelId);
            roomDetails = {
              roomNumber: room.roomNumber,
              type: `${hotel?.name || 'Hotel'} - ${room.roomNumber}`,
              price: room.price,
              capacity: room.capacity,
            };
          }
        }

        return {
          ...ticket,
          event,
          roomDetails,
        };
      })
    );

    return ticketsWithEvents.filter(ticket => ticket.event !== null);
  },
});

export const cancel = mutation({
  args: { ticketId: v.id("tickets") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in");
    }

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) {
      throw new Error("Ticket not found");
    }

    if (ticket.userId !== userId) {
      throw new Error("Can only cancel your own tickets");
    }

    if (ticket.status === "cancelled") {
      throw new Error("Ticket already cancelled");
    }

    const event = await ctx.db.get(ticket.eventId);
    if (!event) {
      throw new Error("Event not found");
    }

    // Update ticket status
    await ctx.db.patch(args.ticketId, { status: "cancelled" });

    // Update sold tickets count only if payment was successful
    if (ticket.paymentStatus === "success") {
      await ctx.db.patch(ticket.eventId, {
        soldTickets: event?.soldTickets || 0 - ticket.quantity,
      });
    }
  },
});

export const getEventTickets = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const event = await ctx.db.get(args.eventId);
    if (!event || event.organizerId !== userId) {
      throw new Error("Unauthorized");
    }

    // Check if user is admin
    const userRole = await ctx.db
      .query("userRoles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (userRole?.role !== "admin") {
      throw new Error("Only admins can view event tickets");
    }

    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();

    return Promise.all(
      tickets.map(async (ticket) => {
        const user = await ctx.db.get(ticket.userId);
        return {
          ...ticket,
          userName: user?.name || "Unknown",
          userEmail: user?.email || "Unknown",
        };
      })
    );
  },
});
