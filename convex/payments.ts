import { action, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "./auth";
import { Doc } from "./_generated/dataModel";
import { allowedAttendeeTypes } from "./schema";
import { TRANSACTION_CHARGE_RATE } from "../src/utils/constants";

export const initializePayment = mutation({
  args: {
    eventId: v.id("events"),
    quantity: v.number(),
    email: v.string(),
    roomId: v.optional(v.id("rooms")),
    attendeeType: v.optional(allowedAttendeeTypes),
    existingTicketId: v.optional(v.id("tickets")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to purchase tickets");
    }
    
    const performingUser = await ctx.db.get(userId);
    if (!performingUser) {
      throw new Error("User not found");
    }

    const event = await ctx.db.get(args.eventId);
    if (!event) {
      throw new Error("Event not found");
    }

    const totalTickets = event.totalTickets ?? 0;
    const soldTickets = event.soldTickets ?? 0;
    const availableTickets = totalTickets - soldTickets;
    if (args.quantity > availableTickets) {
      throw new Error("Not enough tickets available");
    }

    if (args.quantity <= 0) {
      throw new Error("Quantity must be positive");
    }

    let roomPrice = 0;
    if (args.roomId) {
      const room = await ctx.db.get(args.roomId);
      if (!room) {
        throw new Error("Selected room not found.");
      }
      roomPrice = room.price;
    }

    let subtotal = 0;
    if (args.existingTicketId) {
      // This is for adding a room to an existing ticket
      subtotal = roomPrice;
      // Ensure the existing ticket exists and is valid
      const existingTicket = await ctx.db.get(args.existingTicketId);
      if (!existingTicket || existingTicket.eventId !== args.eventId) {
        throw new Error("Invalid existing ticket reference.");
      }
      if (existingTicket.roomId) {
        throw new Error("Room already booked for this ticket.");
      }
    } else {
      // This is for an initial event registration
      const ticketPrice = event.price * args.quantity;
      subtotal = ticketPrice + roomPrice;
    }
    const transactionCharge = subtotal * TRANSACTION_CHARGE_RATE;
    const totalPrice = subtotal + transactionCharge;
    const isFree = event.price === 0 || event.exemptedEmails?.includes(performingUser.email!);

    let ticketIdToProcess = args.existingTicketId;
    if (!ticketIdToProcess) {
      ticketIdToProcess = await ctx.db.insert("tickets", {
        eventId: args.eventId,
        userId,
        quantity: args.quantity,
        totalPrice,
        roomId: args.roomId,
        purchaseDate: Date.now(),
        status: isFree ? "confirmed" : "pending",
        paymentStatus: isFree ? "success" : "pending",
        attendeeType: args.attendeeType,
      });
    }

    return {
      ticketId: ticketIdToProcess,
      isFree,
      amount: isFree ? 0 : totalPrice,
      roomIdToBook: args.roomId,
    };
  },
});

export const verifyPayment = mutation({
  args: {
    ticketId: v.id("tickets"),
    paymentReference: v.string(),
    paymentStatus: v.union(
      v.literal("success"),
      v.literal("failed"),
      v.literal("abandoned")
    ),
    roomIdToBook: v.optional(v.id("rooms")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in");
    }

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) {
      throw new Error("Ticket not found for verification");
    }

    if (ticket.userId !== userId) {
      throw new Error("Unauthorized");
    }

    // Update ticket with payment info
      
      const originalTicketStatus = ticket.status;

    const ticketUpdateData: Partial<Doc<"tickets">> = {
      paymentReference: args.paymentReference,
      paymentStatus: args.paymentStatus,
    };

    if (args.paymentStatus === "success") {
      // Update sold tickets count
      // const event = await ctx.db.get(ticket.eventId);
      // if (event && event.soldTickets !== undefined) {
      //   await ctx.db.patch(ticket.eventId, {
      //     soldTickets: event.soldTickets + ticket.quantity,
      //   });
        
        ticketUpdateData.status = "confirmed"; // Confirm the ticket status

        if (args.roomIdToBook) {
          ticketUpdateData.roomId = args.roomIdToBook; // Assign the room
        }
  
        // Only increment sold tickets if this ticket was newly confirmed (was pending)
        if (originalTicketStatus !== "confirmed") {
          const event = await ctx.db.get(ticket.eventId);
          if (event && event.soldTickets !== undefined) {
            await ctx.db.patch(ticket.eventId, {
              soldTickets: (event.soldTickets ?? 0) + ticket.quantity,
            });
          }
      }
    } else {
      // Payment failed, remove the ticket
      // await ctx.db.delete(args.ticketId);
      await ctx.db.patch(args.ticketId, {
        paymentStatus: args.paymentStatus,
        status: "cancelled",
      });
    }

    return { success: args.paymentStatus === "success" };
  },
});

export const getPaymentStatus = query({
  args: { ticketId: v.id("tickets") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket || ticket.userId !== userId) return null;

    return {
      status: ticket.paymentStatus,
      reference: ticket.paymentReference,
    };
  },
});

export const getTransactions = query({
  args: { eventId: v.optional(v.id("events")) },
  handler: async (ctx, args) => {
    let tickets: Doc<"tickets">[] = [];
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];
    const currentUser = await ctx.db.get(userId);
    if (!currentUser) return [];
    if (currentUser.isEventOrganizerGlobal ) {
      tickets = await ctx.db
        .query("tickets")
        .order("desc")
        .collect();
    } else if (args.eventId && currentUser.managesEvents?.includes(args.eventId)) {
      tickets = await ctx.db
        .query("tickets")
        .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId!))
        .collect();
    } else {
      tickets = [];
    }
    
    const transactionsWithDetails = await Promise.all(
      tickets.map(async (ticket) => {
        const user = ticket.userId ? await ctx.db.get(ticket.userId) : null;
        const event = ticket.eventId ? await ctx.db.get(ticket.eventId) : null;

        return {
          ...ticket,
          userName: user?.name,
          userEmail: user?.email,
          eventName: event?.title,
          // attendeeType should already be on the ticket object if saved
        };
      })
    );
    
    return transactionsWithDetails;
  },
});
