import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "./auth";
import { Doc } from "./_generated/dataModel";
import { allowedAttendeeTypes } from "./schema";
import { TRANSACTION_CHARGE_RATE } from "../src/utils/constants";

export const initializePayment = mutation({
  args: {
    eventId: v.id("events"),
    quantity: v.number(),
    email: v.string(),
    roomId: v.optional(v.id("rooms")),
    attendeeType: v.optional(allowedAttendeeTypes),
    existingTicketId: v.optional(v.id("tickets")),
    extraDays: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to purchase tickets");
    }

    const performingUser = await ctx.db.get(userId);
    if (!performingUser) {
      throw new Error("User not found");
    }

    const event = await ctx.db.get(args.eventId);
    if (!event) {
      throw new Error("Event not found");
    }

    const totalTickets = event.totalTickets ?? 0;
    const soldTickets = event.soldTickets ?? 0;
    const availableTickets = totalTickets - soldTickets;
    if (args.quantity > availableTickets) {
      throw new Error("Not enough tickets available");
    }

    if (args.quantity <= 0) {
      throw new Error("Quantity must be positive");
    }

    let roomCost = 0;
    if (args.roomId) {
      const room = await ctx.db.get(args.roomId);
      if (!room) {
        throw new Error("Selected room not found.");
      }
      // Calculate event days from start and end dates
      const MS_PER_DAY = 1000 * 60 * 60 * 24;
      const startDate = new Date(event.startDate!);
      const endDate = new Date(event.endDate!);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / MS_PER_DAY);
      const eventDays = diffDays > 0 ? diffDays : 1;

      // Calculate room cost for event days
      roomCost = room.pricePerBedPerDay * eventDays;

      // Add cost for extra days, considering the whole room is booked
      if (args.extraDays && args.extraDays > 0) {
        roomCost += room.pricePerBedPerDay * room.capacity * args.extraDays;
      }
    }

    let subtotal = 0;
    if (args.existingTicketId) {
      // This is for adding a room to an existing ticket
      subtotal = roomCost;
      // Ensure the existing ticket exists and is valid
      const existingTicket = await ctx.db.get(args.existingTicketId);
      if (!existingTicket || existingTicket.eventId !== args.eventId) {
        throw new Error("Invalid existing ticket reference.");
      }
      if (existingTicket.roomId) {
        throw new Error("Room already booked for this ticket.");
      }

      // Check if user already has any room booking for this event
      const userTicketsForEvent = await ctx.db
        .query("tickets")
        .withIndex("by_userId_eventId", (q) => q.eq("userId", userId).eq("eventId", args.eventId))
        .collect();

      const hasExistingRoomBooking = userTicketsForEvent.some(ticket =>
        ticket.roomId && ticket.status !== "cancelled"
      );

      if (hasExistingRoomBooking) {
        throw new Error("You already have a room booking for this event.");
      }
    } else {
      // This is for an initial event registration
      // Check if user already has a confirmed ticket for this event
      const existingTickets = await ctx.db
        .query("tickets")
        .withIndex("by_userId_eventId", (q) => q.eq("userId", userId).eq("eventId", args.eventId))
        .filter((q) => q.neq(q.field("status"), "cancelled"))
        .collect();

      if (existingTickets.length > 0) {
        throw new Error("You are already registered for this event. Check your tickets to manage your booking.");
      }
      console.log("Event price:", event.price);
      console.log("Quantity:", args.quantity);
      console.log("Room price:", roomCost);

      const ticketPrice = event.price * args.quantity;
      subtotal = ticketPrice + roomCost;
    }
    const transactionCharge = subtotal * TRANSACTION_CHARGE_RATE;
    const totalPrice = subtotal + transactionCharge;
    console.log("Total price: ", totalPrice);
    console.log('user exempted? ', event.exemptedEmails?.includes(performingUser.email!));
    const isFree = totalPrice === 0 || event.exemptedEmails?.includes(performingUser.email!);
    console.log("Is free? ", isFree);

    let ticketIdToProcess = args.existingTicketId;
    let roomTransactionId = null;

    if (!ticketIdToProcess) {
      // This is a new ticket (initial registration)
      ticketIdToProcess = await ctx.db.insert("tickets", {
        eventId: args.eventId,
        userId,
        quantity: args.quantity,
        totalPrice,
        roomId: args.roomId,
        purchaseDate: Date.now(),
        status: isFree ? "confirmed" : "pending",
        paymentStatus: isFree ? "success" : "pending",
        attendeeType: args.attendeeType,
      });
    } else {
      // This is an add-on room booking - create a room transaction record
      roomTransactionId = await ctx.db.insert("roomTransactions", {
        userId,
        eventId: args.eventId,
        originalTicketId: args.existingTicketId!,
        roomId: args.roomId!,
        totalPrice,
        purchaseDate: Date.now(),
        status: isFree ? "confirmed" : "pending",
        paymentStatus: isFree ? "success" : "pending",
      });
    }

    return {
      ticketId: ticketIdToProcess,
      roomTransactionId,
      isFree,
      amount: isFree ? 0 : totalPrice,
      roomIdToBook: args.roomId,
    };
  },
});

export const verifyPayment = mutation({
  args: {
    ticketId: v.id("tickets"),
    roomTransactionId: v.optional(v.id("roomTransactions")),
    paymentReference: v.string(),
    paymentStatus: v.union(
      v.literal("success"),
      v.literal("failed"),
      v.literal("abandoned")
    ),
    roomIdToBook: v.optional(v.id("rooms")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in");
    }

    // Handle room transaction if this is an add-on booking
    if (args.roomTransactionId) {
      const roomTransaction = await ctx.db.get(args.roomTransactionId);
      if (!roomTransaction) {
        throw new Error("Room transaction not found for verification");
      }

      if (roomTransaction.userId !== userId) {
        throw new Error("Unauthorized");
      }

      const roomTransactionUpdateData: Partial<Doc<"roomTransactions">> = {
        paymentReference: args.paymentReference,
        paymentStatus: args.paymentStatus,
      };

      if (args.paymentStatus === "success") {
        roomTransactionUpdateData.status = "confirmed";

        // Update the original ticket with the room ID
        await ctx.db.patch(roomTransaction.originalTicketId, {
          roomId: roomTransaction.roomId,
        });
      } else {
        roomTransactionUpdateData.status = "cancelled";
      }

      await ctx.db.patch(args.roomTransactionId, roomTransactionUpdateData);
      return { success: args.paymentStatus === "success" };
    }

    // Handle regular ticket payment
    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) {
      throw new Error("Ticket not found for verification");
    }

    if (ticket.userId !== userId) {
      throw new Error("Unauthorized");
    }

    const originalTicketStatus = ticket.status;

    const ticketUpdateData: Partial<Doc<"tickets">> = {
      paymentReference: args.paymentReference,
      paymentStatus: args.paymentStatus,
    };

    if (args.paymentStatus === "success") {
      ticketUpdateData.status = "confirmed"; // Confirm the ticket status

      if (args.roomIdToBook) {
        ticketUpdateData.roomId = args.roomIdToBook; // Assign the room

        // If this was an add-on room booking, update the total price to include room cost
        const room = await ctx.db.get(args.roomIdToBook);
        if (room && ticket.roomId === undefined) { // Only if room wasn't already booked
          const roomPrice = room.pricePerBedPerDay;
          const transactionCharge = roomPrice * TRANSACTION_CHARGE_RATE;
          const additionalCost = roomPrice + transactionCharge;
          ticketUpdateData.totalPrice = ticket.totalPrice + additionalCost;
        }
      }

      // Apply the ticket updates
      await ctx.db.patch(args.ticketId, ticketUpdateData);

      // Only increment sold tickets if this ticket was newly confirmed (was pending)
      if (originalTicketStatus !== "confirmed") {
        const event = await ctx.db.get(ticket.eventId);
        if (event && event.soldTickets !== undefined) {
          await ctx.db.patch(ticket.eventId, {
            soldTickets: (event.soldTickets ?? 0) + ticket.quantity,
          });
        }
      }
    } else {
      // Payment failed, update the ticket with failed status
      ticketUpdateData.status = "cancelled";
      await ctx.db.patch(args.ticketId, ticketUpdateData);
    }

    return { success: args.paymentStatus === "success" };
  },
});

export const getPaymentStatus = query({
  args: { ticketId: v.id("tickets") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket || ticket.userId !== userId) return null;

    return {
      status: ticket.paymentStatus,
      reference: ticket.paymentReference,
    };
  },
});

export const getTransactions = query({
  args: { eventId: v.optional(v.id("events")) },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];
    const currentUser = await ctx.db.get(userId);
    if (!currentUser) return [];

    let tickets: Doc<"tickets">[] = [];
    let roomTransactions: Doc<"roomTransactions">[] = [];

    if (currentUser.isEventOrganizerGlobal) {
      tickets = await ctx.db
        .query("tickets")
        .order("desc")
        .collect();
      roomTransactions = await ctx.db
        .query("roomTransactions")
        .order("desc")
        .collect();
    } else if (args.eventId && currentUser.managesEvents?.includes(args.eventId)) {
      tickets = await ctx.db
        .query("tickets")
        .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId!))
        .collect();
      roomTransactions = await ctx.db
        .query("roomTransactions")
        .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId!))
        .collect();
    } else {
      tickets = [];
      roomTransactions = [];
    }

    // Process regular tickets
    const ticketTransactions = await Promise.all(
      tickets.map(async (ticket) => {
        const user = ticket.userId ? await ctx.db.get(ticket.userId) : null;
        const event = ticket.eventId ? await ctx.db.get(ticket.eventId) : null;

        return {
          ...ticket,
          userName: user?.name,
          userEmail: user?.email,
          eventName: event?.title,
          transactionType: "ticket" as const,
        };
      })
    );

    // Process room transactions
    const roomTransactionDetails = await Promise.all(
      roomTransactions.map(async (roomTxn) => {
        const user = roomTxn.userId ? await ctx.db.get(roomTxn.userId) : null;
        const event = roomTxn.eventId ? await ctx.db.get(roomTxn.eventId) : null;

        return {
          _id: roomTxn._id,
          userId: roomTxn.userId,
          eventId: roomTxn.eventId,
          quantity: 0, // Room transactions don't have ticket quantities
          totalPrice: roomTxn.totalPrice,
          purchaseDate: roomTxn.purchaseDate,
          status: roomTxn.status,
          paymentReference: roomTxn.paymentReference,
          paymentStatus: roomTxn.paymentStatus,
          roomId: roomTxn.roomId,
          userName: user?.name,
          userEmail: user?.email,
          eventName: event?.title,
          transactionType: "room" as const,
          attendeeType: undefined, // Room transactions don't have attendee types
        };
      })
    );

    // Combine and sort by purchase date
    const allTransactions = [...ticketTransactions, ...roomTransactionDetails];
    allTransactions.sort((a, b) => b.purchaseDate - a.purchaseDate);

    return allTransactions;
  },
});
