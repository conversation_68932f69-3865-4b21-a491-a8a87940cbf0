import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { getAuthUserId, getUser } from "./auth";

export const getAttendance = query({
  args: { ticketId: v.id("tickets"), date: v.string() },
  handler: async (ctx, args) => {
    const attendance = await ctx.db
      .query("attendanceRecords")
      .withIndex("by_ticket_date", (q) => 
        q.eq("ticketId", args.ticketId).eq("date", args.date)
      )
      .first();
    return attendance?.isPresent ?? false;
  },
});

export const toggleAttendance = mutation({
  args: {
    ticketId: v.id("tickets"),
    date: v.string(),
    isPresent: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    const performingUser = await getUser(ctx, userId!);
    if (!performingUser) {
      throw new Error("Not authenticated");
    }

    // Get the ticket to verify it exists and get eventId
    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) {
      throw new Error("Ticket not found");
    }

    // Check for existing attendance record
    const existingRecord = await ctx.db
      .query("attendanceRecords")
      .withIndex("by_ticket_date", (q) => 
        q.eq("ticketId", args.ticketId).eq("date", args.date)
      )
      .first();

    if (existingRecord) {
      // Update existing record
      await ctx.db.patch(existingRecord._id, {
        isPresent: args.isPresent,
        recordedAt: Date.now(),
        recordedBy: performingUser._id,
      });
    } else {
      // Create new record
      await ctx.db.insert("attendanceRecords", {
        ticketId: args.ticketId,
        eventId: ticket.eventId,
        date: args.date,
        isPresent: args.isPresent,
        recordedAt: Date.now(),
        recordedBy: performingUser._id,
      });
    }

    // Update the ticket's attendedDates array
    const dateStr = args.date;
    let attendedDates = ticket.attendedDates || [];
    
    if (args.isPresent) {
      // Add date if not already present
      if (!attendedDates.includes(dateStr)) {
        attendedDates = [...attendedDates, dateStr];
      }
    } else {
      // Remove date if present
      attendedDates = attendedDates.filter(d => d !== dateStr);
    }

    await ctx.db.patch(args.ticketId, { attendedDates });

    return { success: true };
  },
});

export const getEventAttendance = query({
  args: { eventId: v.id("events"), date: v.optional(v.string()) },
  handler: async (ctx, args) => {
    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();

    if (!args.date) {
      return tickets.map(ticket => ({
        ...ticket,
        isPresent: false,
      }));
    }

    // Get attendance for the specific date
    const attendanceRecords = await ctx.db
      .query("attendanceRecords")
      .withIndex("by_event_date", (q) => 
        q.eq("eventId", args.eventId).eq("date", args.date!)
      )
      .collect();

    const attendanceMap = new Map(
      attendanceRecords.map(record => [record.ticketId, record.isPresent])
    );

    return tickets.map(ticket => ({
      ...ticket,
      isPresent: attendanceMap.get(ticket._id) || false,
    }));
  },
});
