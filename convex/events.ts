import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { eventLevels, allowedAttendeeTypes } from "./schema";
import { getAuthUserId, getUser } from "./auth";

export const create = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    startDate: v.number(),
    endDate: v.number(),
    startTime: v.string(),
    endTime: v.string(),
    price: v.number(),
    level: eventLevels,
    allowedAttendeeTypes: v.array(allowedAttendeeTypes),
    featuredImageStorageId: v.optional(v.id("_storage")),
    location: v.string(),
    imageUrl: v.optional(v.string()),
    registrationOpenDate: v.string(),
    exemptedEmails: v.optional(v.array(v.string())),
    registrationCloseDate: v.string(),
    totalTickets: v.optional(v.number()), // Max capacity
    category: v.optional(v.string()), // If you still want to use categories for filtering
  },
  handler: async (ctx, args) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("User must be authenticated to create an event.");
    }

    const user = await getUser(ctx, authUserId);

    if (!user) {
      throw new Error("User not found.");
    }

    // Add authorization check: only event organizers can create events
    if (!user.isEventOrganizerGlobal) { // Or more granular permission check
      throw new Error("User is not authorized to create events.");
    }

    // Ensure required fields are present
    const eventData = {
      ...args,
      organizerId: user._id,
      organizerName: user.name || user.email,
      soldTickets: 0,
      // Ensure allowedAttendeeTypes has at least one value
      allowedAttendeeTypes: args.allowedAttendeeTypes || ['Guests']
    };

    const eventId = await ctx.db.insert("events", eventData);
    return eventId;
  },
});

export const update = mutation({
  args: {
    eventId: v.id("events"),
    // Include all fields that can be updated, similar to create args, all optional
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    startTime: v.optional(v.string()),
    endTime: v.optional(v.string()),
    price: v.optional(v.number()),
    level: v.optional(eventLevels),
    allowedAttendeeTypes: v.optional(v.array(allowedAttendeeTypes)),
    featuredImageStorageId: v.optional(v.id("_storage")),
    location: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    registrationOpenDate: v.optional(v.string()),
    registrationCloseDate: v.optional(v.string()),
    totalTickets: v.optional(v.number()),
    exemptedEmails: v.optional(v.array(v.string())),
    category: v.optional(v.string()),
    // Add other updatable fields from your EditEventModal
  },
  handler: async (ctx, { eventId, ...updates }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("User must be authenticated to update an event.");
    }

    const user = await getUser(ctx, authUserId);

    if (!user) {
      throw new Error("User not found.");
    }

    // Add authorization check: only event organizers can update events
    if (!user.isEventOrganizerGlobal) { // Or more granular permission check
      throw new Error("User is not authorized to update events.");
    }

    const event = await ctx.db.get(eventId);
    if (!event) throw new Error("Event not found");

    if (event.organizerId !== user._id) {
      throw new Error("Not authorized to update this event");
    }

    await ctx.db.patch(eventId, updates);
  },
});

// Add list, getById, myEvents queries as needed, adapting from your existing frontend calls
export const list = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("events").order("desc"); // Sort by creation time or start date
    if (args.category && args.category !== "All") {
      query = query.filter((q) => q.eq(q.field("category"), args.category));
    }
    return await query.collect();
  },
});

export const getById = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.eventId);
  },
});

export const myEvents = query({
  handler: async (ctx) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) return [];

    const user = await getUser(ctx, authUserId);
    if (!user) return [];

    const events = await ctx.db.query("events")
      .withIndex("by_organizer", q => q.eq("organizerId", user._id))
      .order("desc")
      .collect();

    return events;
  }
});

export const deleteEvent = mutation({
  args: { eventId: v.id("events") },
  handler: async (ctx, { eventId }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("User must be authenticated to delete an event.");
    }

    const user = await getUser(ctx, authUserId);
    if (!user) {
      throw new Error("User not found.");
    }

    // Add authorization check: only event organizers can delete events
    if (!user.isEventOrganizerGlobal) {
      throw new Error("User is not authorized to delete events.");
    }

    const event = await ctx.db.get(eventId);
    if (!event) {
      throw new Error("Event not found");
    }

    if (event.organizerId !== user._id) {
      throw new Error("Not authorized to delete this event");
    }

    // Check if there are any tickets sold for this event
    const tickets = await ctx.db
      .query("tickets")
      .withIndex("by_eventId", (q) => q.eq("eventId", eventId))
      .collect();

    if (tickets.length > 0) {
      throw new Error("Cannot delete event with existing ticket registrations. Please cancel all tickets first.");
    }

    // Delete the event
    await ctx.db.delete(eventId);

    return { success: true };
  },
});