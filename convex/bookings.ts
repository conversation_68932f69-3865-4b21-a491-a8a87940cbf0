import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "./auth";

export const createBooking = mutation({
  args: {
    roomId: v.id("rooms"),
    bedNumber: v.number(),
    ticketId: v.id("tickets"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to create a booking.");
    }

    // Check if the bed is already booked
    const existingBooking = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("bedNumber"), args.bedNumber))
      .first();

    if (existingBooking) {
      throw new Error("This bed is already booked.");
    }

    const bookingId = await ctx.db.insert("bookings", {
      ...args,
      userId,
      status: "confirmed",
    });

    await ctx.db.insert("hotelBookings", {
      ticketId: args.ticketId,
      bookingId,
    });

    return bookingId;
  },
});

export const getMyBookings = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const userBookings = await ctx.db
      .query("bookings")
      .filter((q) => q.eq(q.field("userId"), userId))
      .collect();

    return Promise.all(
      userBookings.map(async (booking) => {
        const room = await ctx.db.get(booking.roomId);
        const hotel = room ? await ctx.db.get(room.hotelId) : null;
        return {
          ...booking,
          room,
          hotel,
        };
      })
    );
  },
});

export const cancelBooking = mutation({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to cancel a booking.");
    }

    const booking = await ctx.db.get(args.bookingId);

    if (!booking || booking.userId !== userId) {
      throw new Error("Booking not found or you are not authorized to cancel it.");
    }

    await ctx.db.patch(args.bookingId, { status: "cancelled" });
  },
});