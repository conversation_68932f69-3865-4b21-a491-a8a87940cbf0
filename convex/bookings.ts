import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId, getUser } from "./auth";
import { Id } from "./_generated/dataModel";

export const createBooking = mutation({
  args: {
    roomId: v.id("rooms"),
    bedNumber: v.number(),
    ticketId: v.id("tickets"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to create a booking.");
    }

    // Check if the bed is already booked
    const existingBooking = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("bedNumber"), args.bedNumber))
      .first();

    if (existingBooking) {
      throw new Error("This bed is already booked.");
    }

    const bookingId = await ctx.db.insert("bookings", {
      ...args,
      userId,
      status: "confirmed",
    });

    await ctx.db.insert("hotelBookings", {
      ticketId: args.ticketId,
      bookingId,
    });

    return bookingId;
  },
});

export const getMyBookings = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const userBookings = await ctx.db
      .query("bookings")
      .filter((q) => q.eq(q.field("userId"), userId))
      .collect();

    return Promise.all(
      userBookings.map(async (booking) => {
        const room = await ctx.db.get(booking.roomId);
        const hotel = room ? await ctx.db.get(room.hotelId) : null;
        return {
          ...booking,
          room,
          hotel,
        };
      })
    );
  },
});

export const getUserBookings = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Not authenticated");
    }

    // Check if current user is admin or the same user
    const adminUser = await getUser(ctx, currentUserId);
    
    if (!adminUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to change user roles. Requires Global Organizer role.");
    }

    const userBookings = await ctx.db
      .query("bookings")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId as Id<"users">))
      .collect();

    return Promise.all(
      userBookings.map(async (booking) => {
        const room = await ctx.db.get(booking.roomId);
        const hotel = room ? await ctx.db.get(room.hotelId) : null;
        const ticket = booking.ticketId ? await ctx.db.get(booking.ticketId) : null;
        const event = ticket ? await ctx.db.get(ticket.eventId) : null;
        
        return {
          ...booking,
          room,
          hotel,
          ticket: ticket ? {
            _id: ticket._id,
            eventId: ticket.eventId,
            status: ticket.status,
            quantity: ticket.quantity,
            totalPrice: ticket.totalPrice,
          } : null,
          event: event ? {
            _id: event._id,
            title: event.title,
            startDate: event.startDate,
            endDate: event.endDate,
            location: event.location,
          } : null,
        };
      })
    );
  },
});

// Get user's room booking for a specific ticket
export const getRoomBookingForTicket = query({
  args: { ticketId: v.id("tickets") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // First check if there's a booking in the bookings table
    const booking = await ctx.db
      .query("bookings")
      .withIndex("by_ticketId", (q) => q.eq("ticketId", args.ticketId))
      .filter((q) => q.and(
        q.eq(q.field("userId"), userId),
        q.eq(q.field("status"), "confirmed")
      ))
      .first();

    if (booking) {
      const room = await ctx.db.get(booking.roomId);
      const hotel = room ? await ctx.db.get(room.hotelId) : null;
      const ticket = await ctx.db.get(args.ticketId);
      const event = ticket ? await ctx.db.get(ticket.eventId) : null;

      return {
        ...booking,
        room,
        hotel,
        ticket,
        event,
        bookingType: "detailed" as const // Has bed assignment
      };
    }

    // If no detailed booking, check if ticket has a roomId (simple room assignment)
    const ticket = await ctx.db.get(args.ticketId);
    if (ticket && ticket.roomId && ticket.userId === userId) {
      const room = await ctx.db.get(ticket.roomId);
      const hotel = room ? await ctx.db.get(room.hotelId) : null;
      const event = await ctx.db.get(ticket.eventId);

      return {
        _id: ticket._id,
        userId: ticket.userId,
        roomId: ticket.roomId,
        ticketId: ticket._id,
        status: ticket.status,
        bedNumber: null, // No specific bed assigned
        room,
        hotel,
        ticket,
        event,
        bookingType: "simple" as const // Just room assignment
      };
    }

    return null;
  },
});

export const cancelBooking = mutation({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to cancel a booking.");
    }

    const booking = await ctx.db.get(args.bookingId);

    if (!booking || booking.userId !== userId) {
      throw new Error("Booking not found or you are not authorized to cancel it.");
    }

    await ctx.db.patch(args.bookingId, { status: "cancelled" });
  },
});

// Get available beds in a specific room
export const getAvailableBedsInRoom = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Get all confirmed bookings for this room
    const existingBookings = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("status"), "confirmed"))
      .collect();

    const occupiedBeds = existingBookings.map(booking => booking.bedNumber);
    const availableBeds = [];

    for (let bedNumber = 1; bedNumber <= room.capacity; bedNumber++) {
      if (!occupiedBeds.includes(bedNumber)) {
        availableBeds.push(bedNumber);
      }
    }

    return {
      room,
      availableBeds,
      occupiedBeds,
      totalCapacity: room.capacity
    };
  },
});

// Get available rooms in the same hotel with same price
export const getAvailableRoomsInHotel = query({
  args: {
    hotelId: v.id("hotels"),
    currentRoomId: v.optional(v.id("rooms")),
    priceRange: v.optional(v.object({
      min: v.number(),
      max: v.number()
    }))
  },
  handler: async (ctx, args) => {
    let roomsQuery = ctx.db
      .query("rooms")
      .withIndex("by_hotelId", (q) => q.eq("hotelId", args.hotelId));

    const allRooms = await roomsQuery.collect();

    // Filter by price range if provided
    let filteredRooms = allRooms;
    if (args.priceRange) {
      filteredRooms = allRooms.filter(room =>
        room.price >= args.priceRange!.min && room.price <= args.priceRange!.max
      );
    }

    // Get availability for each room
    const roomsWithAvailability = await Promise.all(
      filteredRooms.map(async (room) => {
        const existingBookings = await ctx.db
          .query("bookings")
          .withIndex("by_roomId", (q) => q.eq("roomId", room._id))
          .filter((q) => q.eq(q.field("status"), "confirmed"))
          .collect();

        const occupiedBeds = existingBookings.map(booking => booking.bedNumber);
        const availableBeds = [];

        for (let bedNumber = 1; bedNumber <= room.capacity; bedNumber++) {
          if (!occupiedBeds.includes(bedNumber)) {
            availableBeds.push(bedNumber);
          }
        }

        return {
          ...room,
          availableBeds,
          occupiedBeds,
          hasAvailability: availableBeds.length > 0,
          isCurrentRoom: room._id === args.currentRoomId
        };
      })
    );

    return roomsWithAvailability.filter(room => room.hasAvailability || room.isCurrentRoom);
  },
});

// Change bed number within the same room
export const changeBedNumber = mutation({
  args: {
    bookingId: v.id("bookings"),
    newBedNumber: v.number()
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to change bed number.");
    }

    const booking = await ctx.db.get(args.bookingId);
    if (!booking || booking.userId !== userId) {
      throw new Error("Booking not found or you are not authorized to modify it.");
    }

    if (booking.status !== "confirmed") {
      throw new Error("Can only modify confirmed bookings.");
    }

    // Check if the new bed is available
    const existingBooking = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", booking.roomId))
      .filter((q) => q.and(
        q.eq(q.field("bedNumber"), args.newBedNumber),
        q.eq(q.field("status"), "confirmed"),
        q.neq(q.field("_id"), args.bookingId) // Exclude current booking
      ))
      .first();

    if (existingBooking) {
      throw new Error("This bed is already occupied.");
    }

    // Validate bed number is within room capacity
    const room = await ctx.db.get(booking.roomId);
    if (!room) {
      throw new Error("Room not found.");
    }

    if (args.newBedNumber < 1 || args.newBedNumber > room.capacity) {
      throw new Error(`Bed number must be between 1 and ${room.capacity}.`);
    }

    await ctx.db.patch(args.bookingId, {
      bedNumber: args.newBedNumber
    });

    return { success: true };
  },
});

// Change to a different room in the same hotel
export const changeRoom = mutation({
  args: {
    bookingId: v.id("bookings"),
    newRoomId: v.id("rooms"),
    newBedNumber: v.number()
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to change room.");
    }

    const booking = await ctx.db.get(args.bookingId);
    if (!booking || booking.userId !== userId) {
      throw new Error("Booking not found or you are not authorized to modify it.");
    }

    if (booking.status !== "confirmed") {
      throw new Error("Can only modify confirmed bookings.");
    }

    const currentRoom = await ctx.db.get(booking.roomId);
    const newRoom = await ctx.db.get(args.newRoomId);

    if (!currentRoom || !newRoom) {
      throw new Error("Room not found.");
    }

    // Ensure both rooms are in the same hotel
    if (currentRoom.hotelId !== newRoom.hotelId) {
      throw new Error("Can only change to rooms within the same hotel.");
    }

    // For now, allow room changes within same price range (±10%)
    const priceThreshold = currentRoom.price * 0.1;
    if (Math.abs(newRoom.price - currentRoom.price) > priceThreshold) {
      throw new Error("Can only change to rooms with similar pricing.");
    }

    // Check if the new bed is available
    const existingBooking = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", args.newRoomId))
      .filter((q) => q.and(
        q.eq(q.field("bedNumber"), args.newBedNumber),
        q.eq(q.field("status"), "confirmed")
      ))
      .first();

    if (existingBooking) {
      throw new Error("This bed is already occupied.");
    }

    // Validate bed number is within new room capacity
    if (args.newBedNumber < 1 || args.newBedNumber > newRoom.capacity) {
      throw new Error(`Bed number must be between 1 and ${newRoom.capacity}.`);
    }

    await ctx.db.patch(args.bookingId, {
      roomId: args.newRoomId,
      bedNumber: args.newBedNumber
    });

    return { success: true };
  },
});

// Convert simple room assignment to detailed booking with bed selection
export const assignBedToSimpleRoomBooking = mutation({
  args: {
    ticketId: v.id("tickets"),
    bedNumber: v.number()
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to assign bed.");
    }

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket || ticket.userId !== userId) {
      throw new Error("Ticket not found or you are not authorized to modify it.");
    }

    if (!ticket.roomId) {
      throw new Error("No room assigned to this ticket.");
    }

    if (ticket.status !== "confirmed") {
      throw new Error("Can only assign beds to confirmed tickets.");
    }

    // Check if there's already a detailed booking for this ticket
    const existingBooking = await ctx.db
      .query("bookings")
      .withIndex("by_ticketId", (q) => q.eq("ticketId", args.ticketId))
      .filter((q) => q.eq(q.field("status"), "confirmed"))
      .first();

    if (existingBooking) {
      throw new Error("This ticket already has a detailed bed assignment.");
    }

    const room = await ctx.db.get(ticket.roomId);
    if (!room) {
      throw new Error("Room not found.");
    }

    // Validate bed number is within room capacity
    if (args.bedNumber < 1 || args.bedNumber > room.capacity) {
      throw new Error(`Bed number must be between 1 and ${room.capacity}.`);
    }

    // Check if the bed is available
    const existingBedBooking = await ctx.db
      .query("bookings")
      .withIndex("by_roomId", (q) => q.eq("roomId", ticket.roomId!))
      .filter((q) => q.and(
        q.eq(q.field("bedNumber"), args.bedNumber),
        q.eq(q.field("status"), "confirmed")
      ))
      .first();

    if (existingBedBooking) {
      throw new Error("This bed is already occupied.");
    }

    // Create detailed booking
    const bookingId = await ctx.db.insert("bookings", {
      userId,
      roomId: ticket.roomId,
      bedNumber: args.bedNumber,
      ticketId: args.ticketId,
      status: "confirmed",
    });

    // Create hotel booking link
    await ctx.db.insert("hotelBookings", {
      ticketId: args.ticketId,
      bookingId,
    });

    return { success: true, bookingId };
  },
});

// Update simple room assignment to different room (and optionally assign bed)
export const updateSimpleRoomAssignment = mutation({
  args: {
    ticketId: v.id("tickets"),
    newRoomId: v.id("rooms"),
    bedNumber: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to update room.");
    }

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket || ticket.userId !== userId) {
      throw new Error("Ticket not found or you are not authorized to modify it.");
    }

    if (!ticket.roomId) {
      throw new Error("No room assigned to this ticket.");
    }

    if (ticket.status !== "confirmed") {
      throw new Error("Can only update confirmed tickets.");
    }

    const currentRoom = await ctx.db.get(ticket.roomId);
    const newRoom = await ctx.db.get(args.newRoomId);

    if (!currentRoom || !newRoom) {
      throw new Error("Room not found.");
    }

    // Ensure both rooms are in the same hotel
    if (currentRoom.hotelId !== newRoom.hotelId) {
      throw new Error("Can only change to rooms within the same hotel.");
    }

    // For now, allow room changes within same price range (±10%)
    const priceThreshold = currentRoom.price * 0.1;
    if (Math.abs(newRoom.price - currentRoom.price) > priceThreshold) {
      throw new Error("Can only change to rooms with similar pricing.");
    }

    // If bed number is provided, validate it
    if (args.bedNumber) {
      if (args.bedNumber < 1 || args.bedNumber > newRoom.capacity) {
        throw new Error(`Bed number must be between 1 and ${newRoom.capacity}.`);
      }

      // Check if the bed is available
      const existingBedBooking = await ctx.db
        .query("bookings")
        .withIndex("by_roomId", (q) => q.eq("roomId", args.newRoomId))
        .filter((q) => q.and(
          q.eq(q.field("bedNumber"), args.bedNumber!),
          q.eq(q.field("status"), "confirmed")
        ))
        .first();

      if (existingBedBooking) {
        throw new Error("This bed is already occupied.");
      }
    }

    // Update ticket with new room
    await ctx.db.patch(args.ticketId, {
      roomId: args.newRoomId
    });

    // If bed number is provided, create detailed booking
    if (args.bedNumber) {
      const bookingId = await ctx.db.insert("bookings", {
        userId,
        roomId: args.newRoomId,
        bedNumber: args.bedNumber,
        ticketId: args.ticketId,
        status: "confirmed",
      });

      await ctx.db.insert("hotelBookings", {
        ticketId: args.ticketId,
        bookingId,
      });

      return { success: true, bookingId, bedAssigned: true };
    }

    return { success: true, bedAssigned: false };
  },
});