import { convexAuth, getAuthUserId as getAuthUserIdInternal } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { Anonymous } from "@convex-dev/auth/providers/Anonymous";
import { query, QueryCtx, MutationCtx } from "./_generated/server";
import { Id } from "./_generated/dataModel";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [Password, Anonymous],
});

/**
 * Get the user from the database
 * @param ctx
 * @param userId
 * @returns
 */
export const getUser = async (
  ctx: QueryCtx | MutationCtx,
  userId: Id<"users">
) => {
  return await ctx.db.get(userId);
};

export const getAuthUserId = async (ctx: QueryCtx | MutationCtx): Promise<Id<"users"> | null> => {
  const userId = await getAuthUserIdInternal(ctx);
  if (!userId) {
    return null;
  }
  return userId.split("|").pop() as Id<"users">;
}

export const loggedInUser = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    const user = await getUser(ctx, userId);
    if (!user) {
      return null;
    }
    return user;
  },
});
