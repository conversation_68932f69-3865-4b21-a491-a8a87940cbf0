import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "./auth";
import { Id } from "./_generated/dataModel";

export const createHotel = mutation({
  args: {
    name: v.string(),
    location: v.string(),
    eventId: v.id("events"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to create a hotel.");
    }

    // Add authorization check here if needed

    const hotelId = await ctx.db.insert("hotels", args);
    return hotelId;
  },
});

export const getHotelsByEvent = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("hotels")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();
  },
});

export const addRoom = mutation({
  args: {
    hotelId: v.id("hotels"),
    roomNumber: v.string(),
    capacity: v.number(),
    price: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to add a room.");
    }

    const existingRoom = await ctx.db
      .query("rooms")
      .withIndex("by_hotel_and_number", (q) => q.eq("hotelId", args.hotelId).eq("roomNumber", args.roomNumber))
      .unique();

    if (existingRoom) {
      throw new Error(`Room ${args.roomNumber} already exists for this hotel.`);
    }
    
    const roomId = await ctx.db.insert("rooms", {
      hotelId: args.hotelId,
      roomNumber: args.roomNumber,
      capacity: args.capacity,
      price: args.price,
    });
    return roomId;
  },
});

export const addRoomsInBulk = mutation({
  args: {
    hotelId: v.id("hotels"),
    prefix: v.string(),
    startNumber: v.number(),
    numberOfRooms: v.number(),
    suffix: v.optional(v.string()),
    capacity: v.number(),
    price: v.number(),
  },
  handler: async (ctx, args) => {
    const { hotelId, prefix, startNumber, numberOfRooms, suffix, capacity, price } = args;

    if (numberOfRooms <= 0) throw new Error("Number of rooms must be positive.");
    if (numberOfRooms > 50) throw new Error("Cannot create more than 50 rooms at once."); // Safety limit

    const createdRoomIds: Id<"rooms">[] = [];
    for (let i = 0; i < numberOfRooms; i++) {
      const roomNumberStr = `${prefix}${startNumber + i}${suffix || ""}`;
      // Consider adding conflict checking here similar to addRoom if strict uniqueness is needed per batch.
      // For this example, we'll assume names are unique or overwrite/error is handled by schema/index.
      const roomId = await ctx.db.insert("rooms", { hotelId, roomNumber: roomNumberStr, capacity, price });
      createdRoomIds.push(roomId);
    }
    return { message: `${createdRoomIds.length} rooms created successfully.`, count: createdRoomIds.length };
  },
});

export const getRoomsByHotel = query({
  args: { hotelId: v.id("hotels") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("rooms")
      .withIndex("by_hotelId", (q) => q.eq("hotelId", args.hotelId))
      .collect();
  },
});