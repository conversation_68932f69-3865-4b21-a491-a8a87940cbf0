import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "./auth";
import { Id } from "./_generated/dataModel";

export const createHotel = mutation({
  args: {
    name: v.string(),
    location: v.string(),
    eventId: v.id("events"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to create a hotel.");
    }

    // Add authorization check here if needed

    const hotelId = await ctx.db.insert("hotels", args);
    return hotelId;
  },
});

export const getHotelsByEvent = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("hotels")
      .withIndex("by_eventId", (q) => q.eq("eventId", args.eventId))
      .collect();
  },
});

export const addRoom = mutation({
  args: {
    hotelId: v.id("hotels"),
    roomNumber: v.string(),
    capacity: v.number(),
    price: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated to add a room.");
    }

    const existingRoom = await ctx.db
      .query("rooms")
      .withIndex("by_hotel_and_number", (q) => q.eq("hotelId", args.hotelId).eq("roomNumber", args.roomNumber))
      .unique();

    if (existingRoom) {
      throw new Error(`Room ${args.roomNumber} already exists for this hotel.`);
    }

    const roomId = await ctx.db.insert("rooms", {
      hotelId: args.hotelId,
      roomNumber: args.roomNumber,
      capacity: args.capacity,
      price: args.price,
    });
    return roomId;
  },
});

export const addRoomsInBulk = mutation({
  args: {
    hotelId: v.id("hotels"),
    prefix: v.string(),
    startNumber: v.number(),
    numberOfRooms: v.number(),
    suffix: v.optional(v.string()),
    capacity: v.number(),
    price: v.number(),
  },
  handler: async (ctx, args) => {
    const { hotelId, prefix, startNumber, numberOfRooms, suffix, capacity, price } = args;

    if (numberOfRooms <= 0) throw new Error("Number of rooms must be positive.");
    if (numberOfRooms > 50) throw new Error("Cannot create more than 50 rooms at once."); // Safety limit

    const createdRoomIds: Id<"rooms">[] = [];
    for (let i = 0; i < numberOfRooms; i++) {
      const roomNumberStr = `${prefix}${startNumber + i}${suffix || ""}`;
      // Consider adding conflict checking here similar to addRoom if strict uniqueness is needed per batch.
      // For this example, we'll assume names are unique or overwrite/error is handled by schema/index.
      const roomId = await ctx.db.insert("rooms", { hotelId, roomNumber: roomNumberStr, capacity, price });
      createdRoomIds.push(roomId);
    }
    return { message: `${createdRoomIds.length} rooms created successfully.`, count: createdRoomIds.length };
  },
});

export const getRoomsByHotel = query({
  args: { hotelId: v.id("hotels") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("rooms")
      .withIndex("by_hotelId", (q) => q.eq("hotelId", args.hotelId))
      .collect();
  },
});

// Get available rooms for event registration (filters out fully occupied rooms)
export const getAvailableRoomsByHotel = query({
  args: { hotelId: v.id("hotels") },
  handler: async (ctx, args) => {
    const allRooms = await ctx.db
      .query("rooms")
      .withIndex("by_hotelId", (q) => q.eq("hotelId", args.hotelId))
      .collect();

    // Check availability for each room
    const roomsWithAvailability = await Promise.all(
      allRooms.map(async (room) => {
        // Get confirmed bookings for this room (detailed bookings)
        const detailedBookings = await ctx.db
          .query("bookings")
          .withIndex("by_roomId", (q) => q.eq("roomId", room._id))
          .filter((q) => q.eq(q.field("status"), "confirmed"))
          .collect();

        // Get simple room assignments (tickets with roomId but no detailed booking)
        const simpleAssignments = await ctx.db
          .query("tickets")
          .withIndex("by_roomId", (q) => q.eq("roomId", room._id))
          .filter((q) => q.and(
            q.eq(q.field("status"), "confirmed"),
            q.eq(q.field("paymentStatus"), "success")
          ))
          .collect();

        // Filter out tickets that already have detailed bookings
        const ticketsWithDetailedBookings = detailedBookings.map(b => b.ticketId);
        const pureSimpleAssignments = simpleAssignments.filter(
          ticket => !ticketsWithDetailedBookings.includes(ticket._id)
        );

        // Calculate occupied beds
        const occupiedBeds = detailedBookings.map(booking => booking.bedNumber);
        const simpleAssignmentCount = pureSimpleAssignments.length;

        // Total occupied capacity = detailed bookings + simple assignments
        const totalOccupied = occupiedBeds.length + simpleAssignmentCount;
        const availableCapacity = room.capacity - totalOccupied;

        return {
          ...room,
          availableCapacity,
          totalOccupied,
          hasAvailability: availableCapacity > 0,
          occupiedBeds,
          simpleAssignments: simpleAssignmentCount
        };
      })
    );

    // Return only rooms with availability
    return roomsWithAvailability.filter(room => room.hasAvailability);
  },
});