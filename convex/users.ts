import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "./auth";
import { Id } from "./_generated/dataModel";
import { userTitles, maritalStatuses, employmentStatuses, ksjiRanks, genders, educationalBackgrounds } from "./schema";
import { getUser } from "./auth";
import { Doc } from "./_generated/dataModel";

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      return null;
    }
    const user = await getUser(ctx, authUserId);

    return user;
  }
});

// Helper mutation to create a user if they don't exist
export const ensureUserExists = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
      // throw new Error("Not authenticated.");

    }
    
    const userId = await getAuthUserId(ctx);
    let user: Doc<"users"> | null;

    if (!userId) {
      // Check if this is the very first user being created.
      const allUsers = await ctx.db.query("users").collect();
      const isFirstUser = allUsers.length === 0;

      user = {
        _id: userId || identity.subject as Id<"users">,
        tokenIdentifier: userId || identity.subject,
        _creationTime: Date.now(),
        email: identity.email,
        name: identity.name,
        // If this is the first user, make them a global event organizer.
        isEventOrganizerGlobal: isFirstUser,
        systemRole: isFirstUser ? 'admin' : 'member',
        // Other fields will be undefined by default
      };
      await ctx.db.insert("users", user!);
    } else {
      user = await getUser(ctx, userId);
    }
    
    return user;
  },
});

export const getCurrentUserWithOrgData = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
      throw new Error("Not authenticated.");
    }
    
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    let user = await getUser(ctx, userId);
    
    // If user doesn't exist, return a properly typed user object with default values
    if (!user) {
      return {
        _id: userId,
        _creationTime: Date.now(),
        email: identity.email || undefined,
        name: identity.name || undefined,
        // Initialize all optional fields with undefined
        isEventOrganizerGlobal: false,
        systemRole: 'member',
        title: undefined,
        gender: undefined,
        dateOfBirth: undefined,
        maritalStatus: undefined,
        otherMaritalStatusDetails: undefined,
        phoneNumber: undefined,
        yearOfInitiation: undefined,
        rank: undefined,
        commanderyAuxiliaryId: undefined,
        currentOffice: undefined,
        educationalBackground: undefined,
        employmentStatus: undefined,
        otherEmploymentStatusDetails: undefined,
        careerProfession: undefined,
        currentPlaceOfWork: undefined,
        otherSkillsExpertise: undefined,
        // Initialize event-related fields
        managesEvents: [],
        isStaffForEvents: [],
        attendeeType: undefined,
      };
    }
    
    // Ensure all fields are present in the returned user object
    return {
      _id: user._id,
      _creationTime: user._creationTime,
      email: user.email,
      name: user.name,
      isEventOrganizerGlobal: user.isEventOrganizerGlobal ?? false,
      systemRole: user.systemRole ?? 'member',
      title: user.title,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth,
      maritalStatus: user.maritalStatus,
      otherMaritalStatusDetails: user.otherMaritalStatusDetails,
      phoneNumber: user.phoneNumber,
      yearOfInitiation: user.yearOfInitiation,
      rank: user.rank,
      commanderyAuxiliaryId: user.commanderyAuxiliaryId,
      districtId: user.districtId,
      grandId: user.grandId,
      currentOffice: user.currentOffice,
      educationalBackground: user.educationalBackground,
      employmentStatus: user.employmentStatus,
      otherEmploymentStatusDetails: user.otherEmploymentStatusDetails,
      careerProfession: user.careerProfession,
      currentPlaceOfWork: user.currentPlaceOfWork,
      otherSkillsExpertise: user.otherSkillsExpertise,
      // Include event-related fields with defaults if not present
      managesEvents: (user as any).managesEvents || [],
      isStaffForEvents: (user as any).isStaffForEvents || [],
      attendeeType: (user as any).attendeeType,
    };
  },
});

export const getUserByTokenIdentifier = query({
  args: { tokenIdentifier: v.string() }, // The 65-character ID
  handler: async (ctx, args) => {
    const user = await getUser(ctx, args.tokenIdentifier as Id<"users">);

    if (!user) {
      console.warn(`User with tokenIdentifier ${args.tokenIdentifier} not found.`);
      return null;
    }

    // Now 'user' is your user document, and user._id is its valid Convex document ID.
    // You can use user._id with db.get() for other related documents if needed.
    return user;
  },
});

export const getUserWithRole = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const user = await getUser(ctx, userId);
    if (!user) return null;

    const userRole = await ctx.db
      .query("userRoles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return {
      ...user,
      role: userRole?.role || "member",
    };
  },
});

// Allows authenticated users to update their own KSJI profile information.
export const updateUserProfile = mutation({
  args: {
    updates: v.object({ // Define specific updatable fields from UserProfile.tsx
      title: v.optional(userTitles),
      gender: v.optional(genders),
      dateOfBirth: v.optional(v.string()),
      grandId: v.optional(v.id("grands")),
      districtId: v.optional(v.id("districts")),
      commanderyId: v.optional(v.id("commanderies")),
      maritalStatus: v.optional(maritalStatuses),
      otherMaritalStatusDetails: v.optional(v.string()),
      phoneNumber: v.optional(v.string()),
      yearOfInitiation: v.optional(v.number()),
      rank: v.optional(ksjiRanks),
      commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
      currentOffice: v.optional(v.string()),
      educationalBackground: v.optional(educationalBackgrounds),
      employmentStatus: v.optional(employmentStatuses),
      otherEmploymentStatusDetails: v.optional(v.string()),
      careerProfession: v.optional(v.string()),
      currentPlaceOfWork: v.optional(v.string()),
      otherSkillsExpertise: v.optional(v.string()),
      name: v.optional(v.string()), // If user can update their display name
    }),
  },
  handler: async (ctx, { updates }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated.");
    }
    
    const userToUpdateId = await getAuthUserId(ctx);
    if (!userToUpdateId) {
      throw new Error("User not found to update.");
    }
    const userToUpdate = await getUser(ctx, userToUpdateId);

    if (!userToUpdate) {
      throw new Error("User not found to update.");
    }
    await ctx.db.patch(userToUpdateId, updates);
    return { success: true };
  },
});

export const setUserRole = mutation({
  args: {
    userId: v.id("users"),
    // role: v.union(v.literal("admin"), v.literal("member")),
    // This now directly sets the global organizer status.
    // `systemRole` can also be set if you use it for other admin types.
    isEventOrganizerGlobal: v.boolean(),
  },
  handler: async (ctx, { userId, isEventOrganizerGlobal }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated. Must be logged in to set user roles.");
    }

    // Check if current user is admin
    const adminUser = await getUser(ctx, identity.subject as Id<"users">);
    
    if (!adminUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to change user roles. Requires Global Organizer role.");
    }


    await ctx.db.patch(userId, {
      isEventOrganizerGlobal,
      // Also update systemRole if it's tied to this concept
      systemRole: isEventOrganizerGlobal ? "admin" : "member",
    });
  },
});

export const makeFirstUserAdmin = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be logged in for makeFirstUserAdmin.");
    }

    // Check if there are any global event organizers
    const existingAdmins = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("isEventOrganizerGlobal"), true))
      .collect();

    if (existingAdmins.length === 0) {
      // Make this user a global event organizer if none exist
      const currentUserId = await getAuthUserId(ctx);
      if (!currentUserId) {
        throw new Error("Must be logged in for makeFirstUserAdmin.");
      }
      const currentUser = await getUser(ctx, currentUserId);
      if (currentUser && !currentUser.isEventOrganizerGlobal) {
        await ctx.db.patch(currentUserId, {
          isEventOrganizerGlobal: true,
          systemRole: "admin", // Also set systemRole for consistency
        });
        console.log("User made an admin.");
        return true; // Return true if the user was made an admin
      } else {
        console.log("User is already an admin.");
      }
    }
    return false;
  },
});

export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }

    return await ctx.db.query("users").collect();
  },
});

export const setEventOrganizerGlobalStatus = mutation({
  args: {
    userId: v.id("users"),
    isEventOrganizerGlobal: v.boolean(),
  },
  handler: async(ctx, {userId, isEventOrganizerGlobal }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }
    
    if (currentUser._id === userId && currentUser.isEventOrganizerGlobal && !isEventOrganizerGlobal) {
      const allAdmins = await ctx.db.query("users").filter(q => q.eq(q.field("isEventOrganizerGlobal"), true)).collect();
      if (allAdmins.length === 1 && allAdmins[0]._id === userId) { // Ensure it's specifically this user who is the last admin
          throw new Error("Cannot remove the last global admin's privileges.");
      }
    }
    
    await ctx.db.patch(userId, {
      isEventOrganizerGlobal,
      systemRole: isEventOrganizerGlobal ? "admin" : "member",
    });
    
    return { success: true, message: `User global organizer status ${isEventOrganizerGlobal ? 'granted' : 'revoked'}.` };
  }
})

// Define a type for the updatable user profile fields by an admin
export const UserProfileUpdateByAdminArgs = {
  userId: v.id("users"),
  name: v.optional(v.string()),
  phoneNumber: v.optional(v.string()),
  // organizationId: v.optional(v.id("organizations")), // If you want to allow changing org by ID
  organizationName: v.optional(v.string()), // If admins can update the denormalized org name
  title: v.optional(userTitles), // Assuming UserTitle is a string union
  gender: v.optional(genders), // Assuming Gender is a string union
  dateOfBirth: v.optional(v.string()), // Store as string, can be parsed to date if needed
  grandId: v.optional(v.id("grands")),
  districtId: v.optional(v.id("districts")),
  commanderyId: v.optional(v.id("commanderies")),
  maritalStatus: v.optional(maritalStatuses), // Assuming MaritalStatus is a string union
  otherMaritalStatusDetails: v.optional(v.string()),
  commanderyAuxiliaryId: v.optional(v.string()),
  currentOffice: v.optional(v.string()),
  yearOfInitiation: v.optional(v.number()),
  rank: v.optional(ksjiRanks), // Assuming Rank is a string union
  educationalBackground: v.optional(v.string()),
  employmentStatus: v.optional(employmentStatuses), // Assuming EmploymentStatus is a string union
  otherEmploymentStatusDetails: v.optional(v.string()),
  careerProfession: v.optional(v.string()),
  currentPlaceOfWork: v.optional(v.string()),
  otherSkillsExpertise: v.optional(v.string()),
};

export const updateUserProfileByAdmin = mutation({
  args: UserProfileUpdateByAdminArgs,
  handler: async (ctx, args) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }
    
    const { userId, ...updates } = args;

    const userToUpdate = await ctx.db.get(userId);
    if (!userToUpdate) {
      throw new Error("User to update not found.");
    }

    // Construct the patch object, only including fields that were actually passed (not undefined)
    const patchData: Partial<typeof userToUpdate> = {};
    if (updates.name !== undefined) patchData.name = updates.name === "" ? undefined : updates.name; // Allow clearing name
    if (updates.phoneNumber !== undefined) patchData.phoneNumber = updates.phoneNumber === "" ? undefined : updates.phoneNumber; // Allow clearing phoneNumber
    if (updates.organizationName !== undefined) patchData.organizationName = updates.organizationName === "" ? undefined : updates.organizationName; // Allow clearing orgName

    await ctx.db.patch(userId, patchData);
    return { success: true, message: "User profile updated successfully by admin." };
  },
});