import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "./auth";
import { Id } from "./_generated/dataModel";
import { userTitles, maritalStatuses, employmentStatuses, ksjiRanks, genders, educationalBackgrounds } from "./schema";
import { getUser } from "./auth";
import { Doc } from "./_generated/dataModel";
export const ensureUserExists = mutation(async (ctx) => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  const user = await ctx.db
    .query("users")
    .withIndex("by_tokenIdentifier", (q) =>
      q.eq("tokenIdentifier", identity.tokenIdentifier)
    )
    .first();

  if (user) {
    return user._id;
  }

  const userId = await ctx.db.insert("users", {
    tokenIdentifier: identity.tokenIdentifier,
    email: identity.email,
    name: identity.name,
    onboardingCompleted: false,
  });

  return userId;
});

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      return null;
    }
    const user = await getUser(ctx, authUserId);

    return user;
  }
});

export const getCurrentUserWithOrgData = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
      throw new Error("Not authenticated.");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    let user = await getUser(ctx, userId);

    // If user doesn't exist, return a properly typed user object with default values
    if (!user) {
      return {
        _id: userId,
        _creationTime: Date.now(),
        email: identity.email || undefined,
        name: identity.name || undefined,
        // Initialize all optional fields with undefined
        isEventOrganizerGlobal: false,
        systemRole: 'member',
        title: undefined,
        gender: undefined,
        dateOfBirth: undefined,
        maritalStatus: undefined,
        otherMaritalStatusDetails: undefined,
        phoneNumber: undefined,
        yearOfInitiation: undefined,
        rank: undefined,
        commanderyAuxiliaryId: undefined,
        // currentOffice: undefined,
        educationalBackground: undefined,
        otherEducationalBackgroundDetails: undefined,
        employmentStatus: undefined,
        otherEmploymentStatusDetails: undefined,
        onboardingCompleted: false,
        careerProfession: undefined,
        currentPlaceOfWork: undefined,
        otherSkillsExpertise: undefined,
        // Initialize event-related fields
        managesEvents: [],
        isStaffForEvents: [],
        attendeeType: undefined,
      };
    }

    // Ensure all fields are present in the returned user object
    return {
      _id: user._id,
      _creationTime: user._creationTime,
      email: user.email,
      name: user.name,
      onboardingCompleted: user.onboardingCompleted,
      isEventOrganizerGlobal: user.isEventOrganizerGlobal ?? false,
      systemRole: user.systemRole ?? 'member',
      title: user.title,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth,
      maritalStatus: user.maritalStatus,
      otherMaritalStatusDetails: user.otherMaritalStatusDetails,
      phoneNumber: user.phoneNumber,
      yearOfInitiation: user.yearOfInitiation,
      rank: user.rank,
      commanderyAuxiliaryId: user.commanderyAuxiliaryId,
      districtId: user.districtId,
      grandId: user.grandId,
      // currentOffice: user.currentOffice,
      educationalBackground: user.educationalBackground,
      otherEducationalBackgroundDetails: user.otherEducationalBackgroundDetails,
      employmentStatus: user.employmentStatus,
      otherEmploymentStatusDetails: user.otherEmploymentStatusDetails,
      careerProfession: user.careerProfession,
      currentPlaceOfWork: user.currentPlaceOfWork,
      otherSkillsExpertise: user.otherSkillsExpertise,
      // Include event-related fields with defaults if not present
      managesEvents: (user as any).managesEvents || [],
      isStaffForEvents: (user as any).isStaffForEvents || [],
      attendeeType: (user as any).attendeeType,
    };
  },
});

export const getUserByTokenIdentifier = query({
  args: { tokenIdentifier: v.string() }, // The 65-character ID
  handler: async (ctx, args) => {
    const user = await getUser(ctx, args.tokenIdentifier as Id<"users">);

    if (!user) {
      console.warn(`User with tokenIdentifier ${args.tokenIdentifier} not found.`);
      return null;
    }

    // Now 'user' is your user document, and user._id is its valid Convex document ID.
    // You can use user._id with db.get() for other related documents if needed.
    return user;
  },
});

export const getUserWithRole = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const user = await getUser(ctx, userId);
    if (!user) return null;

    const userRole = await ctx.db
      .query("userRoles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return {
      ...user,
      role: userRole?.role || "member",
    };
  },
});

// Allows authenticated users to update their own KSJI profile information.
export const updateUserProfile = mutation({
  args: {
    updates: v.object({ // Define specific updatable fields from UserProfile.tsx
      title: v.optional(userTitles),
      gender: v.optional(genders),
      dateOfBirth: v.optional(v.string()),
      grandId: v.optional(v.id("grands")),
      districtId: v.optional(v.id("districts")),
      commanderyId: v.optional(v.id("commanderies")),
      maritalStatus: v.optional(maritalStatuses),
      otherMaritalStatusDetails: v.optional(v.string()),
      phoneNumber: v.optional(v.string()),
      yearOfInitiation: v.optional(v.number()),
      rank: v.optional(ksjiRanks),
      officeId: v.optional(v.id("ksjiOffices")),
      commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
      // currentOffice: v.optional(v.string()),
      educationalBackground: v.optional(v.string()),
      otherEducationalBackgroundDetails: v.optional(v.string()),
      employmentStatus: v.optional(employmentStatuses),
      otherEmploymentStatusDetails: v.optional(v.string()),
      careerProfession: v.optional(v.string()),
      currentPlaceOfWork: v.optional(v.string()),
      otherSkillsExpertise: v.optional(v.string()),
      name: v.optional(v.string()), // If user can update their display name
    }),
  },
  handler: async (ctx, { updates }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated.");
    }

    const userToUpdateId = await getAuthUserId(ctx);
    if (!userToUpdateId) {
      throw new Error("User not found to update.");
    }
    const userToUpdate = await getUser(ctx, userToUpdateId);

    if (!userToUpdate) {
      throw new Error("User not found to update.");
    }

    if (updates.phoneNumber) {
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_phoneNumber", (q) =>
          q.eq("phoneNumber", updates.phoneNumber!)
        )
        .first();
      if (existingUser && existingUser._id !== userToUpdateId) {
        throw new Error("Phone number is already in use.");
      }
    }

    await ctx.db.patch(userToUpdateId, updates);
    return { success: true };
  },
});

export const completeOnboarding = mutation({
  args: {
    updates: v.object({
      name: v.optional(v.string()),
      title: v.optional(userTitles),
      gender: v.optional(genders),
      dateOfBirth: v.optional(v.string()),
      grandId: v.optional(v.id("grands")),
      districtId: v.optional(v.id("districts")),
      commanderyId: v.optional(v.id("commanderies")),
      maritalStatus: v.optional(maritalStatuses),
      otherMaritalStatusDetails: v.optional(v.string()),
      phoneNumber: v.optional(v.string()),
      yearOfInitiation: v.optional(v.number()),
      rank: v.optional(ksjiRanks),
      officeId: v.optional(v.id("ksjiOffices")),
      commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
      educationalBackground: v.optional(v.string()),
      otherEducationalBackgroundDetails: v.optional(v.string()),
      employmentStatus: v.optional(employmentStatuses),
      otherEmploymentStatusDetails: v.optional(v.string()),
      careerProfession: v.optional(v.string()),
      currentPlaceOfWork: v.optional(v.string()),
      otherSkillsExpertise: v.optional(v.string()),
    }),
  },
  handler: async (ctx, { updates }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated.");
    }

    if (!updates.name) {
      throw new Error("Name is required to complete onboarding.");
    }

    if (updates.phoneNumber) {
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_phoneNumber", (q) =>
          q.eq("phoneNumber", updates.phoneNumber!)
        )
        .first();
      if (existingUser && existingUser._id !== userId) {
        throw new Error("Phone number is already in use.");
      }
    }

    await ctx.db.patch(userId, {
      ...updates,
      onboardingCompleted: true,
      defaultAttendeeType: updates.officeId ? "Delegates" : "Observers",
    });
  },
});

export const setUserRole = mutation({
  args: {
    userId: v.id("users"),
    // role: v.union(v.literal("admin"), v.literal("member")),
    // This now directly sets the global organizer status.
    // `systemRole` can also be set if you use it for other admin types.
    isEventOrganizerGlobal: v.boolean(),
  },
  handler: async (ctx, { userId, isEventOrganizerGlobal }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated. Must be logged in to set user roles.");
    }

    // Check if current user is admin
    const adminUser = await getUser(ctx, identity.subject as Id<"users">);

    if (!adminUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to change user roles. Requires Global Organizer role.");
    }


    await ctx.db.patch(userId, {
      isEventOrganizerGlobal,
      systemRole: isEventOrganizerGlobal ? "admin" : "member",
    });
  },
});

export const makeFirstUserAdmin = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be logged in for makeFirstUserAdmin.");
    }

    // Check if there are any global event organizers
    const existingAdmins = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("isEventOrganizerGlobal"), true))
      .collect();

    if (existingAdmins.length === 0) {
      // Make this user a global event organizer if none exist
      const currentUserId = await getAuthUserId(ctx);
      if (!currentUserId) {
        throw new Error("Must be logged in for makeFirstUserAdmin.");
      }
      const currentUser = await getUser(ctx, currentUserId);
      if (currentUser && !currentUser.isEventOrganizerGlobal) {
        await ctx.db.patch(currentUserId, {
          isEventOrganizerGlobal: true,
          systemRole: "admin", // Also set systemRole for consistency
        });
        console.log("User made an admin.");
        return true; // Return true if the user was made an admin
      } else {
        console.log("User is already an admin.");
      }
    }
    return false;
  },
});

export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }

    const registeredUsers = await ctx.db.query("users").collect();
    const preRegisteredUsers = await ctx.db.query("preRegisteredUsers").collect();

    const registeredUserEmails = new Set(registeredUsers.map(user => user.email));

    const combinedUsers = [
      ...registeredUsers.map(user => ({ ...user, status: "Registered" })),
      ...preRegisteredUsers
        .filter(preUser => !registeredUserEmails.has(preUser.email))
        .map(preUser => ({
          ...preUser,
          _id: preUser._id as unknown as Id<"users">, // Cast for compatibility
          status: "Pre-registered"
        })),
    ];

    return combinedUsers;
  },
});

export const createUsersFromCsv = mutation({
  args: {
    users: v.array(v.object({
      email: v.string(),
      name: v.optional(v.string()),
      title: v.optional(userTitles),
      gender: v.optional(genders),
      phoneNumber: v.optional(v.string()),
      memberNumber: v.optional(v.string()),
      organizationName: v.optional(v.string()),
      dateOfBirth: v.optional(v.string()),
      maritalStatus: v.optional(maritalStatuses),
      yearOfInitiation: v.optional(v.number()),
      rank: v.optional(ksjiRanks),
      grandId: v.optional(v.id("grands")),
      districtId: v.optional(v.id("districts")),
      commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
      officeId: v.optional(v.id("ksjiOffices")),
      otherMaritalStatusDetails: v.optional(v.string()),
      educationalBackground: v.optional(v.string()),
      otherEducationalBackgroundDetails: v.optional(v.string()),
      employmentStatus: v.optional(employmentStatuses),
      otherEmploymentStatusDetails: v.optional(v.string()),
      careerProfession: v.optional(v.string()),
      currentPlaceOfWork: v.optional(v.string()),
      otherSkillsExpertise: v.optional(v.string()),
    }))
  },
  handler: async (ctx, { users }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Not authenticated.");
    }

    const currentUser = await getUser(ctx, authUserId);
    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Unauthorized: Only administrators can import users.");
    }

    const results = [];
    const errors = [];

    for (const userData of users) {
      try {
        const existingUser = await ctx.db
          .query("users")
          .withIndex("by_email", (q) => q.eq("email", userData.email.toLowerCase()))
          .first();

        if (existingUser) {
          errors.push({ email: userData.email, error: "User already exists" });
          continue;
        }

        const existingPreRegisteredUser = await ctx.db
          .query("preRegisteredUsers")
          .withIndex("by_email", (q) => q.eq("email", userData.email.toLowerCase()))
          .first();

        if (existingPreRegisteredUser) {
          errors.push({
            email: userData.email,
            error: "User already pre-registered",
          });
          continue;
        }

        const userId = await ctx.db.insert("preRegisteredUsers", {
          ...userData,
          email: userData.email.toLowerCase(),
        });

        results.push({ email: userData.email, userId });
      } catch (error) {
        errors.push({ email: userData.email, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }

    return { success: true, created: results.length, errors };
  },
});

// in convex/users.ts

// For bulk deleting users
export const deleteUsers = mutation({
  args: { userIds: v.array(v.id("users")) },
  handler: async (ctx, { userIds }) => {
    const identity = await ctx.auth.getUserIdentity();
    // ... (add your authorization logic here)
    for (const userId of userIds) {
      await ctx.db.delete(userId);
    }
    return { success: true, message: `${userIds.length} user(s) deleted.` };
  },
});

// For bulk updating organizer status
export const setEventOrganizerGlobalStatusBulk = mutation({
  args: {
    userIds: v.array(v.id("users")),
    isEventOrganizerGlobal: v.boolean(),
  },
  handler: async (ctx, { userIds, isEventOrganizerGlobal }) => {
    const identity = await ctx.auth.getUserIdentity();
    // ... (add your authorization logic here)
    for (const userId of userIds) {
      await ctx.db.patch(userId, { isEventOrganizerGlobal });
    }
    return { success: true, message: `Updated ${userIds.length} user(s).` };
  },
});


export const setEventOrganizerGlobalStatus = mutation({
  args: {
    userId: v.id("users"),
    isEventOrganizerGlobal: v.boolean(),
  },
  handler: async (ctx, { userId, isEventOrganizerGlobal }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }

    if (currentUser._id === userId && currentUser.isEventOrganizerGlobal && !isEventOrganizerGlobal) {
      const allAdmins = await ctx.db.query("users").filter(q => q.eq(q.field("isEventOrganizerGlobal"), true)).collect();
      if (allAdmins.length === 1 && allAdmins[0]._id === userId) { // Ensure it's specifically this user who is the last admin
        throw new Error("Cannot remove the last global admin's privileges.");
      }
    }

    await ctx.db.patch(userId, {
      isEventOrganizerGlobal,
      systemRole: isEventOrganizerGlobal ? "admin" : "member",
    });

    return { success: true, message: `User global organizer status ${isEventOrganizerGlobal ? 'granted' : 'revoked'}.` };
  }
})

// Define a type for the updatable user profile fields by an admin
export const UserProfileUpdateByAdminArgs = {
  userId: v.id("users"),
  name: v.optional(v.string()),
  phoneNumber: v.optional(v.string()),
  // organizationId: v.optional(v.id("organizations")), // If you want to allow changing org by ID
  organizationName: v.optional(v.string()), // If admins can update the denormalized org name
  title: v.optional(userTitles), // Assuming UserTitle is a string union
  gender: v.optional(genders), // Assuming Gender is a string union
  dateOfBirth: v.optional(v.string()), // Store as string, can be parsed to date if needed
  grandId: v.optional(v.id("grands")),
  districtId: v.optional(v.id("districts")),
  commanderyId: v.optional(v.id("commanderies")),
  maritalStatus: v.optional(maritalStatuses), // Assuming MaritalStatus is a string union
  otherMaritalStatusDetails: v.optional(v.string()),
  commanderyAuxiliaryId: v.optional(v.id("commanderiesAuxiliaries")),
  // currentOffice: v.optional(v.string()),
  yearOfInitiation: v.optional(v.number()),
  rank: v.optional(ksjiRanks), // Assuming Rank is a string union
  officeId: v.optional(v.id("ksjiOffices")),
  educationalBackground: v.optional(v.string()),
  otherEducationalBackgroundDetails: v.optional(v.string()),
  employmentStatus: v.optional(employmentStatuses), // Assuming EmploymentStatus is a string union
  otherEmploymentStatusDetails: v.optional(v.string()),
  careerProfession: v.optional(v.string()),
  currentPlaceOfWork: v.optional(v.string()),
  otherSkillsExpertise: v.optional(v.string()),
};

export const updateUserProfileByAdmin = mutation({
  args: UserProfileUpdateByAdminArgs,
  handler: async (ctx, args) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      throw new Error("Must be logged in for getAllUsers.");
    }
    const currentUser = await getUser(ctx, authUserId);

    if (!currentUser?.isEventOrganizerGlobal) {
      throw new Error("Not authorized to view all users. Requires Global Organizer role.");
    }

    const { userId, ...updates } = args;

    if (updates.phoneNumber) {
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_phoneNumber", (q) =>
          q.eq("phoneNumber", updates.phoneNumber!)
        )
        .first();
      if (existingUser && existingUser._id !== userId) {
        throw new Error("Phone number is already in use.");
      }
    }

    const userToUpdate = await ctx.db.get(userId);
    if (!userToUpdate) {
      throw new Error("User to update not found.");
    }

    await ctx.db.patch(userId, updates);
    return { success: true, message: "User profile updated successfully by admin." };
  },
});

export const searchUsers = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    if (!args.query) {
      return [];
    }
    return await ctx.db
      .query("users")
      .withSearchIndex("search_users", (q) => q.search("name", args.query))
      .collect();
  },
});


export const finishSignUp = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    const user = await getUser(ctx, userId);
    if (!user || !user.email) {
      return;
    }
    const preRegisteredUser = await ctx.db
      .query("preRegisteredUsers")
      .withIndex("by_email", (q) => q.eq("email", user.email!.toLowerCase()))
      .first();

    if (preRegisteredUser) {
      const { _id, _creationTime, ...rest } = preRegisteredUser;
      await ctx.db.patch(user._id, {
        ...rest,
      });
    }
    const allUsers = await ctx.db.query("users").collect();
    if (allUsers.length === 1) {
      await ctx.db.patch(user._id, {
        isEventOrganizerGlobal: true,
        systemRole: "admin",
      });
    }
  },
});
