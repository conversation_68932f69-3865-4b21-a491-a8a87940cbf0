import { query } from "./_generated/server";
import { v } from "convex/values";

export const listGrands = query({
  handler: async (ctx) => {
    return await ctx.db.query("grands").collect();
  },
});

export const listDistricts = query({
  handler: async (ctx) => {
    return await ctx.db.query("districts")
      .order("desc")
      .collect();
  },
});

export const listDistrictsByGrand = query({
  args: { grandId: v.id("grands") },
  handler: async (ctx, { grandId }) => {
    return await ctx.db
      .query("districts")
      .withIndex("by_grandId", (q) => q.eq("grandId", grandId))
      .collect();
  },
});

export const listCommanderiesByDistrict = query({
  args: { districtId: v.id("districts") },
  handler: async (ctx, { districtId }) => {
    return await ctx.db
      .query("commanderiesAuxiliaries")
      .withIndex("by_districtId", (q) => q.eq("districtId", districtId))
      .collect();
  },
});

export const listCommanderiesAuxiliaries = query({
  handler: async (ctx) => {
    return await ctx.db.query("commanderiesAuxiliaries").collect();
  },
});

export const listOffices = query({
  handler: async (ctx) => {
    return await ctx.db.query("ksjiOffices").collect();
  },
});