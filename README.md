# KSJI Events Platform

A full-featured event management platform built with [Convex](https://convex.dev) and React, allowing users to browse events, purchase tickets, and manage event registrations.

## Features

- **User Authentication**: Secure sign-in using Convex Auth
- **Event Management**: Create, browse, and register for events
- **Ticket Management**: Purchase and manage event tickets
- **Hotel Booking**: Book accommodations for events
- **User Profiles**: Complete user profiles with personal information
- **Admin Dashboard**: For event organizers to manage events, users, and transactions
- **Payment Integration**: Process payments using Paystack

## Project Structure

- **Frontend**: React application in the `src` directory built with Vite
- **Backend**: Convex functions in the `convex` directory
- **Deployment**: Connected to Convex deployment [`quirky-lemming-749`](https://dashboard.convex.dev/d/quirky-lemming-749)

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables:
   - Create a `.env.local` file with your Convex deployment URL
   - Run `npm run setup` to configure Convex Auth

### Development

Start the development servers:
```
npm run dev
```

This will start both the frontend Vite server and the Convex backend.

## Deployment

1. Deploy your Convex functions:
   ```
   npx convex deploy
   ```

2. Build and deploy the frontend:
   ```
   npm run build
   ```

## Technologies

- **Frontend**: React 19, TailwindCSS
- **Backend**: Convex
- **Authentication**: Convex Auth
- **Payment Processing**: Paystack
- **Build Tool**: Vite

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
