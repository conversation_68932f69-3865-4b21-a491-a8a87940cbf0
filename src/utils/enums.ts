/**
 * Shared enum constants extracted from Convex schema
 * This ensures consistency between frontend and backend validation
 */

// User Titles - matches convex/schema.ts userTitles
export const USER_TITLES = [
  "Mr.", "Mrs.", "Ms.", "Dr.", "Prof.",
  "Ing.", "Hon.", "Rev.", "Ps.", "Aps.",
  "ESQ.", "Suv.", "Chief", "<PERSON>"
] as const;

export type UserTitle = typeof USER_TITLES[number];

// Genders - matches convex/schema.ts genders
export const GENDERS = [
  "Male", "Female"
] as const;

export type Gender = typeof GENDERS[number];

// Marital Statuses - matches convex/schema.ts maritalStatuses
export const MARITAL_STATUSES = [
  "Single", "Married", "Divorced", "Separated",
  "Widowed", "Other"
] as const;

export type MaritalStatus = typeof MARITAL_STATUSES[number];

// Employment Statuses - matches convex/schema.ts employmentStatuses
export const EMPLOYMENT_STATUSES = [
  "Employed", "Self-Employed", "Unemployed",
  "Student", "Other"
] as const;

export type EmploymentStatus = typeof EMPLOYMENT_STATUSES[number];

// KSJI Ranks - matches convex/schema.ts ksjiRanks
export const KSJI_RANKS = [
  "1st Lieutenant (1Lt.)",
  "2nd Lieutenant (2Lt)",
  "Captain (Capt.)",
  "Major (Maj.)",
  "Lieutenant Colonel (Lt. Col)",
  "Colonel (Col)",
  "Brigadier General (BGen.)",
  "Major General (MGen.)",
  "Lieutenant General (Lt. Gen.)",
  "General",
  "Noble Sister (NS)"
] as const;

export type KSJIRank = typeof KSJI_RANKS[number];

// Educational Backgrounds - matches convex/schema.ts educationalBackgrounds
export const EDUCATIONAL_BACKGROUNDS = [
  "Primary", "Secondary", "Tertiary", "Vocational", "Post-Tertiary", "Other"
] as const;

export type EducationalBackground = typeof EDUCATIONAL_BACKGROUNDS[number];

// Event Attendee Types - matches convex/schema.ts allowedAttendeeTypes
export const ATTENDEE_TYPES = [
  "VIP", "Delegates", "Observers", "Guests"
] as const;

export type AttendeeType = typeof ATTENDEE_TYPES[number];

// Event Levels - matches convex/schema.ts eventLevels
export const EVENT_LEVELS = [
  "National", "Grand", "District", "Local"
] as const;

export type EventLevel = typeof EVENT_LEVELS[number];

// Payment Statuses - matches convex/schema.ts paymentStatuses
export const PAYMENT_STATUSES = [
  "pending", "success", "failed", "abandoned", "waived", "free"
] as const;

export type PaymentStatus = typeof PAYMENT_STATUSES[number];

// Ticket/Booking Statuses
export const TICKET_STATUSES = [
  "confirmed", "cancelled", "pending"
] as const;

export type TicketStatus = typeof TICKET_STATUSES[number];

// Organization Types
export const ORGANIZATION_TYPES = [
  "Commandery", "Auxiliary"
] as const;

export type OrganizationType = typeof ORGANIZATION_TYPES[number];

// Office Types
export const OFFICE_TYPES = [
  "Knights", "Ladies' Auxiliary"
] as const;

export type OfficeType = typeof OFFICE_TYPES[number];

// Helper functions for common operations
export const getUserTitleOptions = () => USER_TITLES.map(title => ({ value: title, label: title }));
export const getGenderOptions = () => GENDERS.map(gender => ({ value: gender, label: gender }));
export const getMaritalStatusOptions = () => MARITAL_STATUSES.map(status => ({ value: status, label: status }));
export const getEmploymentStatusOptions = () => EMPLOYMENT_STATUSES.map(status => ({ value: status, label: status }));
export const getKSJIRankOptions = () => KSJI_RANKS.map(rank => ({ value: rank, label: rank }));
export const getEducationalBackgroundOptions = () => EDUCATIONAL_BACKGROUNDS.map(bg => ({ value: bg, label: bg }));
export const getAttendeeTypeOptions = () => ATTENDEE_TYPES.map(type => ({ value: type, label: type }));
export const getEventLevelOptions = () => EVENT_LEVELS.map(level => ({ value: level, label: level }));

// Validation helpers
export const isValidUserTitle = (title: string): title is UserTitle => USER_TITLES.includes(title as UserTitle);
export const isValidGender = (gender: string): gender is Gender => GENDERS.includes(gender as Gender);
export const isValidMaritalStatus = (status: string): status is MaritalStatus => MARITAL_STATUSES.includes(status as MaritalStatus);
export const isValidEmploymentStatus = (status: string): status is EmploymentStatus => EMPLOYMENT_STATUSES.includes(status as EmploymentStatus);
export const isValidKSJIRank = (rank: string): rank is KSJIRank => KSJI_RANKS.includes(rank as KSJIRank);
export const isValidEducationalBackground = (bg: string): bg is EducationalBackground => EDUCATIONAL_BACKGROUNDS.includes(bg as EducationalBackground);
export const isValidAttendeeType = (type: string): type is AttendeeType => ATTENDEE_TYPES.includes(type as AttendeeType);
export const isValidEventLevel = (level: string): level is EventLevel => EVENT_LEVELS.includes(level as EventLevel);
