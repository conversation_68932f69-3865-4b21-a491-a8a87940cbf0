import {
  Authenticated,
  Unauthenticated,
  useQuery,
  useMutation,
  useConvexAuth,
} from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { Toaster } from "sonner";
import { EventList } from "./components/EventList";
import { CreateEvent } from "./components/CreateEvent";
import { MyEvents } from "./components/MyEvents";
import { MyTickets } from "./components/MyTickets";
import { UserManagement } from "./components/UserManagement";
import { UserProfile } from "./components/UserProfile";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import DashboardLayout from "./components/layout/DashboardLayout";
import { TabType } from "./types";
import { TransactionsLog } from "./components/TransactionsLog";
import { HotelManagement } from "./components/HotelManagement";
import { AdminAccommodationManagement } from "./components/AdminAccommodationManagement";
import { OnboardingForm } from "./components/OnboardingForm";

export default function App() {
  const [activeTab, setActiveTab] = useState<TabType>("browse");
  const currentUserData = useQuery(api.users.getCurrentUserWithOrgData);
  const makeFirstUserAdmin = useMutation(api.users.makeFirstUserAdmin);
  const { isLoading: isAuthLoading, isAuthenticated } = useConvexAuth();
  const finishSignUp = useMutation(api.users.finishSignUp);

  useEffect(() => {
    const checkFirstAdmin = async () => {
      if (
        !isAuthLoading &&
        isAuthenticated &&
        currentUserData &&
        currentUserData._id
      ) {
        if (
          currentUserData.systemRole === "member" &&
          !currentUserData.isEventOrganizerGlobal
        ) {
          try {
            const becameAdmin = await makeFirstUserAdmin();
            if (becameAdmin) {
              toast.success(
                "You've been made a Global Organizer as the first user!"
              );
              window.location.reload();
            }
          } catch (error: any) {
            if (!error.message?.includes("Not authenticated")) {
              // console.error("Error in checkFirstAdmin:", error);
            }
          }
        }
      }
    };

    if (isAuthenticated && currentUserData && !currentUserData.onboardingCompleted) {
      finishSignUp().catch((error) => {
        console.error("Error finishing sign up:", error);
      });
    }

    checkFirstAdmin();
  }, [isAuthLoading, isAuthenticated, currentUserData, makeFirstUserAdmin, finishSignUp]);


  const isEventOrganizer =
    currentUserData?.isEventOrganizerGlobal ||
    (currentUserData?.managesEvents && currentUserData.managesEvents.length > 0);

  return (
    <>
      <Unauthenticated>
        <SignInForm />
      </Unauthenticated>
      <Authenticated>
        {isAuthenticated && !isAuthLoading && currentUserData && !currentUserData.onboardingCompleted ? (
          <OnboardingForm />
        ) : (
          <DashboardLayout
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            isEventOrganizer={isEventOrganizer}
            currentUser={currentUserData}
          >
            <Content
              activeTab={activeTab}
              isEventOrganizer={isEventOrganizer}
            />
          </DashboardLayout>
        )}
      </Authenticated>
      <Toaster />
    </>
  );
}

type ContentProps = {
  activeTab: TabType;
  isEventOrganizer: boolean;
};

function Content({ activeTab, isEventOrganizer }: ContentProps) {
  return (
    <>
      {activeTab === "browse" && <EventList />}
      {activeTab === "profile" && <UserProfile />}
      {activeTab === "create_event" && isEventOrganizer && <CreateEvent />}
      {activeTab === "my_events" && isEventOrganizer && <MyEvents />}
      {activeTab === "my_registrations" && <MyTickets />}
      {activeTab === "my_accommodations" && <HotelManagement />}
      {activeTab === "manage_users" && isEventOrganizer && <UserManagement />}
      {activeTab === "transactions" && isEventOrganizer && <TransactionsLog />}
      {activeTab === "hotel_management" && isEventOrganizer && <AdminAccommodationManagement />}
      {/* Add other components for new tabs like Transactions */}

      {(activeTab === "create_event" ||
        activeTab === "my_events" ||
        activeTab === "manage_users") &&
        !isEventOrganizer && (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Access Denied
              </h3>
              <p className="text-red-600">
                Only administrators can access this feature.
              </p>
            </div>
          </div>
        )}
    </>
  );
}
