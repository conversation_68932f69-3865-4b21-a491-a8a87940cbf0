// Define types that match the Convex schema but don't depend on it
import {
  UserTitle,
  Gender,
  MaritalStatus,
  EmploymentStatus,
  KSJIRank
} from "../utils/enums";

// Re-export types for backward compatibility
export type { UserTitle, Gender, MaritalStatus, EmploymentStatus };

export type UserId = string;
export type CommanderiesAuxiliariesId = string;
export type UserRank = KSJIRank; // Alias for backward compatibility

export interface UserProfile {
  _id: UserId;
  _creationTime: number;
  name?: string;
  email?: string;
  isEventOrganizerGlobal?: boolean;
  systemRole?: string;
  title?: UserTitle;
  gender?: Gender;
  dateOfBirth?: string;
  maritalStatus?: MaritalStatus;
  otherMaritalStatusDetails?: string;
  memberNumber?: string;
  yearOfInitiation?: number;
  rank?: UserRank;
  commanderyAuxiliaryId?: CommanderiesAuxiliariesId;
  currentOffice?: string;
  educationalBackground?: string;
  employmentStatus?: EmploymentStatus;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
  // Event-related fields that might be needed
  managesEvents?: string[];
  isStaffForEvents?: string[];
  attendeeType?: string;
}

export type UserProfileUpdate = Partial<Omit<UserProfile, '_id' | '_creationTime'>>;
