// Define types that match the Convex schema but don't depend on it
export type UserId = string;
export type CommanderiesAuxiliariesId = string;

export type UserTitle = "Mr." | "Mrs." | "Ms." | "Dr." | "Prof." | "Ing." | "Hon." | "Rev." | "Ps." | "Aps." | "ESQ." | "Suv." | "Chief" | "Nana";
export type Gender = "Male" | "Female";
export type MaritalStatus = "Single" | "Married" | "Divorced" | "Widowed" | "Other";
export type EmploymentStatus = "Employed" | "Self-Employed" | "Unemployed" | "Student" | "Retired" | "Other";
export type UserRank = "1st Lieutenant (1Lt.)" | "2nd Lieutenant (2Lt.)" | "Captain (Capt.)" | "Major (Maj.)" | "Lieutenant Colonel (LtCol.)" | "Colonel (Col.)" | "General (Gen.)" | "Chief" | "Nana";

export interface UserProfile {
  _id: UserId;
  _creationTime: number;
  name?: string;
  email?: string;
  isEventOrganizerGlobal?: boolean;
  systemRole?: string;
  title?: UserTitle;
  gender?: Gender;
  dateOfBirth?: string;
  maritalStatus?: MaritalStatus;
  otherMaritalStatusDetails?: string;
  memberNumber?: string;
  yearOfInitiation?: number;
  rank?: UserRank;
  commanderyAuxiliaryId?: CommanderiesAuxiliariesId;
  currentOffice?: string;
  educationalBackground?: string;
  employmentStatus?: EmploymentStatus;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
  // Event-related fields that might be needed
  managesEvents?: string[];
  isStaffForEvents?: string[];
  attendeeType?: string;
}

export type UserProfileUpdate = Partial<Omit<UserProfile, '_id' | '_creationTime'>>;
