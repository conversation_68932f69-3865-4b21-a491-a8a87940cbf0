import { Id } from "../../convex/_generated/dataModel";

export type TabType =
  | "browse"
  | "create_event"
  | "my_events"
  | "my_registrations"
  | "manage_users"
  | "profile"
  | "hotel_management"
  | "transactions";

export type EventType = {
  _id: Id<"events">;
  title: string;
  description: string;
  startDate?: number;
  location: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  imageUrl?: string;
  category?: string;
  organizerName: string;
};

export interface RoomBasicInfo { // For ticket display
  _id: Id<"rooms">;
  roomNumber?: string;
  type?: string;
  // any other details you want to show on the ticket card
}

export type TicketType = {
  _id: Id<"tickets">;
  event: EventType;
  quantity: number;
  totalPrice: number; // Original total price for the event ticket
  purchaseDate: number;
  status: "confirmed" | "cancelled" | "pending";
  roomId?: Id<"rooms"> | null;
  roomDetails?: RoomBasicInfo | null; // Populated by backend if roomId exists
  attendeeType?: string;
  userEmail: string;
};

export type EventLevel = "National" | "Grand" | "District" | "Local";

export type EventAttendees = "VIP" | "Delegates" | "Observers" | "Guests";

export type TransactionType = {
  _id: string;
  totalPrice: number;
  currency: string;
  description: string;
  type: "debit" | "credit";
  paymentReference?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  eventName?: string;
  attendeeType?: EventAttendees;
  purchaseDate: number;
  paymentStatus: "pending" | "success" | "failed";
  status: "pending" | "success" | "confirmed" | "failed";
  eventId?: string;
};
