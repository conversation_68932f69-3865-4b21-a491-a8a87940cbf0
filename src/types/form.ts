import { Id } from "../../convex/_generated/dataModel";
import {
  UserTitle,
  Gender,
  MaritalStatus,
  EmploymentStatus,
  EducationalBackground,
  KSJIRank as Rank,
  USER_TITLES,
  GENDERS,
  MARITAL_STATUSES,
  EMPLOYMENT_STATUSES,
  KSJI_RANKS,
  EDUCATIONAL_BACKGROUNDS
} from "../utils/enums";

// Re-export types for backward compatibility
export type { UserTitle, Gender, MaritalStatus, EmploymentStatus, EducationalBackground };

export interface UserProfileFormData {
  // Personal Information
  name?: string;
  title?: UserTitle;
  gender?: Gender;
  dateOfBirth?: string;
  maritalStatus?: MaritalStatus;
  otherMaritalStatusDetails?: string;
  phoneNumber?: string;

  // KSJI Information
  memberNumber?: string;
  organizationName?: string;
  yearOfInitiation?: string;
  rank?: Rank;
  commanderyAuxiliaryId?: Id<"commanderiesAuxiliaries">;
  districtId?: Id<"districts">;
  grandId?: Id<"grands">;
  officeId?: Id<"ksjiOffices">;
  currentOffice?: Id<"ksjiOffices">;

  // Education & Employment
  educationalBackground?: EducationalBackground;
  otherEducationalBackgroundDetails?: string;
  employmentStatus?: EmploymentStatus;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
}

export const formOptions = {
  titles: [...USER_TITLES],
  genders: [...GENDERS],
  ranks: [...KSJI_RANKS],
  maritalStatuses: [...MARITAL_STATUSES],
  employmentStatuses: [...EMPLOYMENT_STATUSES],
  educationalBackgrounds: [...EDUCATIONAL_BACKGROUNDS]
} as const;

// Define a type for the user data we expect from the backend
export interface ManagedUser {
  _id: Id<"users">;
  name?: string | null;
  email: string;
  memberNumber?: string | null;
  organizationName?: string | null;
  isEventOrganizerGlobal?: boolean | null;
  // Add any other fields you want to display
}

export interface UserTicket {
  _id: Id<"tickets">;
  eventId: Id<"events">;
  event: {
    title: string;
    startDate: number;
    location: string;
  };
  status: "confirmed" | "cancelled" | "pending";
  purchaseDate: number;
  roomId?: Id<"rooms"> | null;
  roomDetails?: {
    hotelName?: string;
    roomNumber?: string;
  } | null;
}

export interface UserBooking {
  _id: Id<"bookings">;
  hotel?: {
    name: string;
  };
  room?: {
    roomNumber: string;
  };
  bedNumber?: string;
  status: string;
  eventTitle?: string;
  ticketId?: Id<"tickets">;
}

// CSV Import Types
export interface CsvUser {
  email: string;
  name?: string;
  title?: string;
  gender?: string;
  phoneNumber?: string;
  memberNumber?: string;
  organizationName?: string;
  dateOfBirth?: string;
  maritalStatus?: string;
  yearOfInitiation?: number;
  rank?: string;
  grand?: string;
  grandId?: string;
  district?: string;
  districtId?: string;
  commanderyAuxiliary?: string;
  commanderyAuxiliaryId?: string;
  office?: string;
  officeId?: string;
  otherMaritalStatusDetails?: string;
  currentOffice?: string;
  educationalBackground?: string;
  otherEducationalBackgroundDetails?: string;
  employmentStatus?: string;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
}

// Import Status Types
export interface ImportStatus {
  success: boolean;
  message: string;
  created: number;
  updated: number;
  errors: { email?: string; error: string }[];
}

// API Response Types
export interface CreateUsersFromCsvResponse {
  success: boolean;
  message: string;
  created: number;
  updated: number;
  errors: { email: string; error: string }[];
}

export function normalizeEnumValue(
  value: string | undefined,
  options: {
    addPeriodForTitles?: boolean,
    removeSpacesInBrackets?: boolean,
    aliasMap?: Record<string, string>,
    allowedValues?: string[],
    nullValues?: string[],
    trim?: boolean
  } = {}
): string | undefined {
  if (!value || typeof value !== 'string') return undefined;
  let result = value;
  if (options.trim !== false) result = result.trim();
  // Remove spaces inside brackets
  if (options.removeSpacesInBrackets) {
    result = result.replace(/\(\s+/g, '(').replace(/\s+\)/g, ')');
  }
  // Add period for titles if missing
  if (options.addPeriodForTitles) {
    result = result.replace(/^(Mr|Mrs|Ms|Dr|Prof|Ing|Hon|Rev|Ps|Aps|ESQ|Suv)$/i, (m) => m + '.');
  }
  // Remove duplicate spaces
  result = result.replace(/\s{2,}/g, ' ');
  // Map aliases
  if (options.aliasMap) {
    const key = result.toLowerCase();
    if (options.aliasMap[key]) {
      result = options.aliasMap[key];
    }
  }
  // Null/ignore values
  if (options.nullValues && options.nullValues.map(v => v.toLowerCase()).includes(result.toLowerCase())) {
    return undefined;
  }
  // Only allow valid enum values
  if (options.allowedValues && !options.allowedValues.includes(result)) {
    return undefined;
  }
  return result;
}