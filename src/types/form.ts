import { Id } from "../../convex/_generated/dataModel";

export type UserTitle = "Mr." | "Mrs." | "Ms." | "Dr." | "Prof." | "Ing." | "Hon." | "Rev." | "Ps." | "Aps." | "ESQ." | "Suv." | "Chief" | "Nana";
export type Gender = "Male" | "Female";
export type MaritalStatus = "Single" | "Married" | "Divorced" | "Widowed" | "Other";
export type EmploymentStatus = "Employed" | "Self-Employed" | "Unemployed" | "Student" | "Other";
export type EducationalBackground = "Primary" | "Secondary" | "Tertiary" | "Vocational" | "Post-Tertiary" | "Other";

export interface UserProfileFormData {
  // Personal Information
  name?: string;
  title?: UserTitle;
  gender?: Gender;
  dateOfBirth?: string;
  maritalStatus?: MaritalStatus;
  otherMaritalStatusDetails?: string;
  phoneNumber?: string;
  
  // KSJI Information
  memberNumber?: string;
  organizationName?: string;
  yearOfInitiation?: string;
  rank?: string;
  commanderyAuxiliaryId?: Id<"commanderiesAuxiliaries">;
  districtId?: Id<"districts">;
  grandId?: Id<"grands">;
  currentOffice?: string;
  
  // Education & Employment
  educationalBackground?: EducationalBackground;
  otherEducationalBackgroundDetails?: string;
  employmentStatus?: EmploymentStatus;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
}

export const formOptions = {
  titles: ["Mr.", "Mrs.", "Ms.", "Dr.", "Prof.", "Ing.", "Hon.", "Rev.", "Ps.", "Aps.", "ESQ.", "Suv.", "Chief", "Nana"],
  genders: ["Male", "Female"],
  maritalStatuses: ["Single", "Married", "Divorced", "Widowed", "Other"],
  employmentStatuses: ["Employed", "Self-Employed", "Unemployed", "Student", "Other"],
  educationalBackgrounds: ["Primary", "Secondary", "Tertiary", "Vocational", "Post-Tertiary", "Other"]
} as const;
