import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Hotel, Bed, MapPin, Calendar, Settings, Users, CreditCard, CheckCircle, Clock, XCircle, User } from "lucide-react";
import { Id } from "../../convex/_generated/dataModel";
import { RoomManagementModal } from "./RoomManagementModal";

interface HotelBooking {
  _id: string;
  event: {
    _id: string;
    title: string;
    startDate: number;
    location: string;
  };
  hotel: {
    _id: string;
    name: string;
    location: string;
  };
  room: {
    _id: string;
    roomNumber: string;
    capacity: number;
    price: number;
  };
  user: {
    _id: string;
    name: string;
    email: string;
    memberNumber?: string;
    rank?: string;
  };
  bedNumber?: number;
  status: "confirmed" | "cancelled" | "pending";
  bookingType: "detailed" | "simple";
  ticketId: string;
}

export function HotelManagement() {
  const [selectedBooking, setSelectedBooking] = useState<HotelBooking | null>(null);
  const [showRoomManagement, setShowRoomManagement] = useState(false);
  const [filterStatus, setFilterStatus] = useState<"all" | "confirmed" | "pending" | "cancelled">("all");

  // Get user's tickets to extract hotel bookings
  const userTickets = useQuery(api.tickets.myTickets);
  const userBookings = useQuery(api.bookings.getMyBookings);
  const currentUser = useQuery(api.auth.loggedInUser);

  // Process hotel bookings from tickets and detailed bookings
  const processHotelBookings = (): HotelBooking[] => {
    const bookings: HotelBooking[] = [];

    // Return empty array if user data is not loaded yet
    if (!currentUser) {
      return bookings;
    }

    const userInfo = {
      _id: currentUser._id || 'unknown',
      name: currentUser.name || 'Unknown User',
      email: currentUser.email || 'No email',
      memberNumber: currentUser.memberNumber || undefined,
      rank: currentUser.rank || undefined,
    };

    // Add bookings from tickets with rooms (simple bookings)
    if (userTickets) {
      userTickets.forEach(ticket => {
        if (ticket.roomId && ticket.roomDetails && ticket.event) {
          bookings.push({
            _id: `ticket-${ticket._id}`,
            event: {
              _id: ticket.event._id,
              title: ticket.event.title,
              startDate: ticket.event.startDate,
              location: ticket.event.location,
            },
            hotel: {
              _id: 'hotel-unknown',
              name: ticket.roomDetails.hotelName || 'Hotel',
              location: 'Location not specified',
            },
            room: {
              _id: ticket.roomId,
              roomNumber: ticket.roomDetails.roomNumber || 'Unknown',
              capacity: ticket.roomDetails.capacity || 1,
              price: ticket.roomDetails.price || 0,
            },
            user: userInfo,
            bedNumber: undefined,
            status: ticket.status as "confirmed" | "cancelled" | "pending",
            bookingType: "simple",
            ticketId: ticket._id,
          });
        }
      });
    }

    // Add detailed bookings
    if (userBookings) {
      userBookings.forEach(booking => {
        if (booking.room && booking.hotel) {
          // Find the corresponding ticket to get event info
          const correspondingTicket = userTickets?.find(t => t._id === booking.ticketId);
          if (correspondingTicket?.event) {
            bookings.push({
              _id: `booking-${booking._id}`,
              event: {
                _id: correspondingTicket.event._id,
                title: correspondingTicket.event.title,
                startDate: correspondingTicket.event.startDate,
                location: correspondingTicket.event.location,
              },
              hotel: {
                _id: booking.hotel._id,
                name: booking.hotel.name,
                location: booking.hotel.location,
              },
              room: {
                _id: booking.room._id,
                roomNumber: booking.room.roomNumber,
                capacity: booking.room.capacity,
                price: booking.room.price,
              },
              user: userInfo,
              bedNumber: booking.bedNumber,
              status: booking.status as "confirmed" | "cancelled" | "pending",
              bookingType: "detailed",
              ticketId: booking.ticketId,
            });
          }
        }
      });
    }

    return bookings;
  };

  const hotelBookings = processHotelBookings();

  // Filter bookings based on status
  const filteredBookings = hotelBookings.filter(booking => {
    if (filterStatus === "all") return true;
    return booking.status === filterStatus;
  });

  // Group bookings by hotel
  const groupedBookings = filteredBookings.reduce((groups, booking) => {
    const hotelKey = booking.hotel.name;
    if (!groups[hotelKey]) {
      groups[hotelKey] = {
        hotel: booking.hotel,
        bookings: [],
      };
    }
    groups[hotelKey].bookings.push(booking);
    return groups;
  }, {} as Record<string, { hotel: HotelBooking['hotel']; bookings: HotelBooking[] }>);

  const handleManageRoom = (booking: HotelBooking) => {
    setSelectedBooking(booking);
    setShowRoomManagement(true);
  };

  const handleRoomManagementSuccess = () => {
    toast.success("Room booking updated successfully!");
    setShowRoomManagement(false);
    setSelectedBooking(null);
  };

  if (userTickets === undefined || userBookings === undefined || currentUser === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-2">Loading hotel bookings...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">My Hotel Bookings</h2>
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <User className="w-4 h-4 mr-2" />
              <span>
                {currentUser.rank ? `${currentUser.rank} ${currentUser.name}` : currentUser.name}
                {currentUser.memberNumber && ` • Member #${currentUser.memberNumber}`}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Bookings</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Hotel className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <p className="text-sm text-blue-600">Total Hotels</p>
                <p className="text-2xl font-bold text-blue-900">{Object.keys(groupedBookings).length}</p>
              </div>
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm text-green-600">Confirmed</p>
                <p className="text-2xl font-bold text-green-900">
                  {hotelBookings.filter(b => b.status === "confirmed").length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-600 mr-3" />
              <div>
                <p className="text-sm text-yellow-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {hotelBookings.filter(b => b.status === "pending").length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <div className="flex items-center">
              <XCircle className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm text-red-600">Cancelled</p>
                <p className="text-2xl font-bold text-red-900">
                  {hotelBookings.filter(b => b.status === "cancelled").length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* No Bookings State */}
      {hotelBookings.length === 0 && (
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <Hotel className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Hotel Bookings</h3>
          <p className="text-gray-600 mb-4">
            You haven't booked any hotel rooms yet. Book a room when registering for events.
          </p>
        </div>
      )}

      {/* Hotel Bookings by Hotel */}
      {Object.entries(groupedBookings).map(([hotelName, { hotel, bookings }]) => (
        <div key={hotelName} className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Hotel Header */}
          <div className="bg-gray-50 px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Hotel className="w-6 h-6 text-gray-600 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{hotel.name}</h3>
                  <p className="text-sm text-gray-600 flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {hotel.location}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  {bookings.length} booking{bookings.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </div>

          {/* Bookings List */}
          <div className="divide-y divide-gray-200">
            {bookings.map((booking) => (
              <div key={booking._id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Event Info */}
                    <div className="flex items-center mb-2">
                      <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                      <h4 className="font-semibold text-gray-900">{booking.event.title}</h4>
                      <span className={`ml-3 px-2 py-1 text-xs rounded-full ${booking.status === "confirmed"
                        ? "bg-green-100 text-green-800"
                        : booking.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                        }`}>
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </div>

                    {/* User Info */}
                    <div className="flex items-center mb-3 p-2 bg-blue-50 rounded-md">
                      <User className="w-4 h-4 text-blue-600 mr-2" />
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-blue-900">
                          {booking.user.rank ? `${booking.user.rank} ${booking.user.name}` : booking.user.name}
                        </span>
                        <div className="flex items-center text-xs text-blue-700">
                          <span>{booking.user.email}</span>
                          {booking.user.memberNumber && (
                            <>
                              <span className="mx-1">•</span>
                              <span>Member #{booking.user.memberNumber}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Room Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <Bed className="w-4 h-4 mr-2" />
                        <span>Room {booking.room.roomNumber}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2" />
                        <span>Capacity: {booking.room.capacity}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <CreditCard className="w-4 h-4 mr-2" />
                        <span>GH₵{booking.room.price.toLocaleString()}</span>
                      </div>
                    </div>

                    {/* Bed Assignment */}
                    {booking.bedNumber ? (
                      <div className="text-sm text-gray-600 mb-3">
                        <strong>Bed Assignment:</strong> Bed {booking.bedNumber}
                      </div>
                    ) : (
                      <div className="text-sm text-yellow-600 mb-3">
                        <strong>Bed Assignment:</strong> Not assigned yet
                      </div>
                    )}

                    {/* Event Date and Location */}
                    <div className="text-sm text-gray-500">
                      <p>Event Date: {new Date(booking.event.startDate).toLocaleDateString()}</p>
                      <p>Event Location: {booking.event.location}</p>
                    </div>
                  </div>

                  {/* Actions */}
                  {booking.status === "confirmed" && (
                    <div className="ml-4">
                      <button
                        onClick={() => handleManageRoom(booking)}
                        className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        <Settings className="w-4 h-4 mr-1" />
                        Manage
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Room Management Modal */}
      {showRoomManagement && selectedBooking && (
        <RoomManagementModal
          ticketId={selectedBooking.ticketId as any}
          onClose={() => {
            setShowRoomManagement(false);
            setSelectedBooking(null);
          }}
          onSuccess={handleRoomManagementSuccess}
        />
      )}
    </div>
  );
}

