import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Hotel, Bed, MapPin, Calendar, Settings, Users, CreditCard, CheckCircle, Clock, XCircle, User, Plus, Layers, Building2 } from "lucide-react";
import { Id } from "../../convex/_generated/dataModel";
import { RoomManagementModal } from "./RoomManagementModal";

interface HotelBooking {
  _id: string;
  event: {
    _id: string;
    title: string;
    startDate: number;
    location: string;
  };
  hotel: {
    _id: string;
    name: string;
    location: string;
  };
  room: {
    _id: string;
    roomNumber: string;
    capacity: number;
    price: number;
  };
  user: {
    _id: string;
    name: string;
    email: string;
    memberNumber?: string;
    rank?: string;
  };
  bedNumber?: number;
  status: "confirmed" | "cancelled" | "pending";
  bookingType: "detailed" | "simple";
  ticketId: string;
}

export function HotelManagement() {
  const [selectedBooking, setSelectedBooking] = useState<HotelBooking | null>(null);
  const [showRoomManagement, setShowRoomManagement] = useState(false);
  const [filterStatus, setFilterStatus] = useState<"all" | "confirmed" | "pending" | "cancelled">("all");

  // Admin hotel management states
  const [showAdminSection, setShowAdminSection] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<Id<"events"> | null>(null);
  const [selectedHotelId, setSelectedHotelId] = useState<Id<"hotels"> | null>(null);
  const [hotelName, setHotelName] = useState("");
  const [hotelLocation, setHotelLocation] = useState("");

  // Get user's tickets to extract hotel bookings
  const userTickets = useQuery(api.tickets.myTickets);
  const userBookings = useQuery(api.bookings.getMyBookings);
  const currentUser = useQuery(api.auth.loggedInUser);

  // Admin queries - only load events if user is an organizer
  const myEvents = useQuery(
    api.events.myEvents,
    currentUser?.isEventOrganizerGlobal === true ? {} : "skip"
  );
  const hotels = useQuery(api.hotels.getHotelsByEvent, selectedEventId ? { eventId: selectedEventId } : "skip");

  // Debug events loading
  console.log("🏨 HotelManagement - User is organizer:", currentUser?.isEventOrganizerGlobal);
  console.log("🏨 HotelManagement - My events:", myEvents?.map(e => ({ id: e._id, title: e.title })));
  console.log("🏨 HotelManagement - Selected event ID:", selectedEventId);

  // Admin mutations
  const createHotel = useMutation(api.hotels.createHotel);

  // Process hotel bookings from tickets and detailed bookings
  const processHotelBookings = (): HotelBooking[] => {
    const bookings: HotelBooking[] = [];

    // Return empty array if user data is not loaded yet
    if (!currentUser) {
      return bookings;
    }

    const userInfo = {
      _id: currentUser._id || 'unknown',
      name: currentUser.name || 'Unknown User',
      email: currentUser.email || 'No email',
      memberNumber: currentUser.memberNumber || undefined,
      rank: currentUser.rank || undefined,
    };

    // Add bookings from tickets with rooms (simple bookings)
    if (userTickets) {
      userTickets.forEach(ticket => {
        if (ticket.roomId && ticket.roomDetails && ticket.event) {
          bookings.push({
            _id: `ticket-${ticket._id}`,
            event: {
              _id: ticket.event._id,
              title: ticket.event.title,
              startDate: ticket.event.startDate || 0,
              location: ticket.event.location,
            },
            hotel: {
              _id: 'hotel-unknown',
              name: ticket.roomDetails.hotelName || 'Hotel',
              location: 'Location not specified',
            },
            room: {
              _id: ticket.roomId,
              roomNumber: ticket.roomDetails.roomNumber || 'Unknown',
              capacity: ticket.roomDetails.capacity || 1,
              price: ticket.roomDetails.price || 0,
            },
            user: userInfo,
            bedNumber: undefined,
            status: ticket.status as "confirmed" | "cancelled" | "pending",
            bookingType: "simple",
            ticketId: ticket._id,
          });
        }
      });
    }

    // Add detailed bookings
    if (userBookings) {
      userBookings.forEach(booking => {
        if (booking.room && booking.hotel) {
          // Find the corresponding ticket to get event info
          const correspondingTicket = userTickets?.find(t => t._id === booking.ticketId);
          if (correspondingTicket?.event) {
            bookings.push({
              _id: `booking-${booking._id}`,
              event: {
                _id: correspondingTicket.event._id,
                title: correspondingTicket.event.title,
                startDate: correspondingTicket.event.startDate || 0,
                location: correspondingTicket.event.location,
              },
              hotel: {
                _id: booking.hotel._id,
                name: booking.hotel.name,
                location: booking.hotel.location,
              },
              room: {
                _id: booking.room._id,
                roomNumber: booking.room.roomNumber,
                capacity: booking.room.capacity,
                price: booking.room.price,
              },
              user: userInfo,
              bedNumber: booking.bedNumber,
              status: booking.status as "confirmed" | "cancelled" | "pending",
              bookingType: "detailed",
              ticketId: booking.ticketId,
            });
          }
        }
      });
    }

    return bookings;
  };

  const hotelBookings = processHotelBookings();

  // Filter bookings based on status
  const filteredBookings = hotelBookings.filter(booking => {
    if (filterStatus === "all") return true;
    return booking.status === filterStatus;
  });

  // Group bookings by hotel
  const groupedBookings = filteredBookings.reduce((groups, booking) => {
    const hotelKey = booking.hotel.name;
    if (!groups[hotelKey]) {
      groups[hotelKey] = {
        hotel: booking.hotel,
        bookings: [],
      };
    }
    groups[hotelKey].bookings.push(booking);
    return groups;
  }, {} as Record<string, { hotel: HotelBooking['hotel']; bookings: HotelBooking[] }>);

  const handleManageRoom = (booking: HotelBooking) => {
    setSelectedBooking(booking);
    setShowRoomManagement(true);
  };

  const handleRoomManagementSuccess = () => {
    toast.success("Room booking updated successfully!");
    setShowRoomManagement(false);
    setSelectedBooking(null);
  };

  const handleCreateHotel = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedEventId) {
      toast.error("Please select an event first.");
      return;
    }
    try {
      await createHotel({ name: hotelName, location: hotelLocation, eventId: selectedEventId });
      toast.success("Hotel created successfully");
      setHotelName("");
      setHotelLocation("");
    } catch (error) {
      toast.error("Failed to create hotel");
    }
  };

  if (userTickets === undefined || userBookings === undefined || currentUser === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-2">Loading hotel bookings...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">My Hotel Bookings</h2>
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <User className="w-4 h-4 mr-2" />
              <span>
                {currentUser?.rank ? `${currentUser.rank} ${currentUser.name}` : currentUser?.name}
                {currentUser?.memberNumber && ` • Member #${currentUser.memberNumber}`}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Bookings</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Hotel className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <p className="text-sm text-blue-600">Total Hotels</p>
                <p className="text-2xl font-bold text-blue-900">{Object.keys(groupedBookings).length}</p>
              </div>
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm text-green-600">Confirmed</p>
                <p className="text-2xl font-bold text-green-900">
                  {hotelBookings.filter(b => b.status === "confirmed").length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-600 mr-3" />
              <div>
                <p className="text-sm text-yellow-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {hotelBookings.filter(b => b.status === "pending").length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <div className="flex items-center">
              <XCircle className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm text-red-600">Cancelled</p>
                <p className="text-2xl font-bold text-red-900">
                  {hotelBookings.filter(b => b.status === "cancelled").length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* No Bookings State */}
      {hotelBookings.length === 0 && (
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <Hotel className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Hotel Bookings</h3>
          <p className="text-gray-600 mb-4">
            You haven't booked any hotel rooms yet. Book a room when registering for events.
          </p>
        </div>
      )}

      {/* Hotel Bookings by Hotel */}
      {Object.entries(groupedBookings).map(([hotelName, { hotel, bookings }]) => (
        <div key={hotelName} className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Hotel Header */}
          <div className="bg-gray-50 px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Hotel className="w-6 h-6 text-gray-600 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{hotel.name}</h3>
                  <p className="text-sm text-gray-600 flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {hotel.location}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  {bookings.length} booking{bookings.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </div>

          {/* Bookings List */}
          <div className="divide-y divide-gray-200">
            {bookings.map((booking) => (
              <div key={booking._id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Event Info */}
                    <div className="flex items-center mb-2">
                      <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                      <h4 className="font-semibold text-gray-900">{booking.event.title}</h4>
                      <span className={`ml-3 px-2 py-1 text-xs rounded-full ${booking.status === "confirmed"
                        ? "bg-green-100 text-green-800"
                        : booking.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                        }`}>
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </div>

                    {/* User Info */}
                    <div className="flex items-center mb-3 p-2 bg-blue-50 rounded-md">
                      <User className="w-4 h-4 text-blue-600 mr-2" />
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-blue-900">
                          {booking.user.rank ? `${booking.user.rank} ${booking.user.name}` : booking.user.name}
                        </span>
                        <div className="flex items-center text-xs text-blue-700">
                          <span>{booking.user.email}</span>
                          {booking.user.memberNumber && (
                            <>
                              <span className="mx-1">•</span>
                              <span>Member #{booking.user.memberNumber}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Room Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <Bed className="w-4 h-4 mr-2" />
                        <span>Room {booking.room.roomNumber}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2" />
                        <span>Capacity: {booking.room.capacity}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <CreditCard className="w-4 h-4 mr-2" />
                        <span>GH₵{booking.room.price.toLocaleString()}</span>
                      </div>
                    </div>

                    {/* Bed Assignment */}
                    {booking.bedNumber ? (
                      <div className="text-sm text-gray-600 mb-3">
                        <strong>Bed Assignment:</strong> Bed {booking.bedNumber}
                      </div>
                    ) : (
                      <div className="text-sm text-yellow-600 mb-3">
                        <strong>Bed Assignment:</strong> Not assigned yet
                      </div>
                    )}

                    {/* Event Date and Location */}
                    <div className="text-sm text-gray-500">
                      <p>Event Date: {new Date(booking.event.startDate).toLocaleDateString()}</p>
                      <p>Event Location: {booking.event.location}</p>
                    </div>
                  </div>

                  {/* Actions */}
                  {booking.status === "confirmed" && (
                    <div className="ml-4">
                      <button
                        onClick={() => handleManageRoom(booking)}
                        className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        <Settings className="w-4 h-4 mr-1" />
                        Manage
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Admin Hotel Management Section */}
      {currentUser?.isEventOrganizerGlobal && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Building2 className="w-6 h-6 text-white mr-3" />
                <h3 className="text-lg font-semibold text-white">Hotel Administration</h3>
              </div>
              <button
                onClick={() => setShowAdminSection(!showAdminSection)}
                className="text-white hover:text-blue-200 transition-colors"
              >
                {showAdminSection ? 'Hide' : 'Show'} Admin Tools
              </button>
            </div>
          </div>

          {showAdminSection && (
            <div className="p-6 space-y-6">
              {/* Event Selection */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3">Select Event to Manage</h4>
                <select
                  value={selectedEventId || ""}
                  onChange={(e) => {
                    const eventId = e.target.value as Id<"events"> || null;
                    console.log("🎯 HotelManagement - Selected event:", eventId);
                    const selectedEvent = myEvents?.find(ev => ev._id === eventId);
                    console.log("🎯 HotelManagement - Selected event details:", selectedEvent);
                    setSelectedEventId(eventId);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-md"
                >
                  <option value="">Select an event...</option>
                  {myEvents?.map((event) => (
                    <option key={event._id} value={event._id}>
                      {event.title}
                    </option>
                  ))}
                </select>
              </div>

              {selectedEventId && (
                <>
                  {/* Hotel Creation */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="text-md font-semibold text-gray-900 mb-3">
                      Create New Hotel for "{
                        myEvents?.find(e => e._id === selectedEventId)?.title ||
                        (myEvents === undefined ? 'Loading...' : 'Selected Event')
                      }"
                    </h4>
                    <form onSubmit={handleCreateHotel} className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <input
                        type="text"
                        placeholder="Hotel Name"
                        value={hotelName}
                        onChange={(e) => setHotelName(e.target.value)}
                        className="p-3 border border-gray-300 rounded-md"
                        required
                      />
                      <input
                        type="text"
                        placeholder="Hotel Location"
                        value={hotelLocation}
                        onChange={(e) => setHotelLocation(e.target.value)}
                        className="p-3 border border-gray-300 rounded-md"
                        required
                      />
                      <button
                        type="submit"
                        className="flex items-center justify-center bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Create Hotel
                      </button>
                    </form>
                  </div>

                  {/* Hotel Management */}
                  {hotels && hotels.length > 0 && (
                    <div className="border rounded-lg p-4">
                      <h4 className="text-md font-semibold text-gray-900 mb-3">Manage Hotels</h4>
                      <div className="space-y-4">
                        {hotels.map((hotel) => (
                          <div key={hotel._id} className="border rounded-md p-4 bg-white">
                            <div className="flex justify-between items-center mb-2">
                              <div>
                                <h5 className="font-semibold">{hotel.name}</h5>
                                <p className="text-sm text-gray-600">{hotel.location}</p>
                              </div>
                              <button
                                onClick={() => setSelectedHotelId(selectedHotelId === hotel._id ? null : hotel._id)}
                                className={`px-3 py-1 rounded text-sm ${selectedHotelId === hotel._id
                                  ? "bg-blue-600 text-white"
                                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                  }`}
                              >
                                {selectedHotelId === hotel._id ? "Hide Rooms" : "Manage Rooms"}
                              </button>
                            </div>

                            {selectedHotelId === hotel._id && (
                              <BulkRoomManagement hotelId={hotel._id} />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      )}

      {/* Room Management Modal */}
      {showRoomManagement && selectedBooking && (
        <RoomManagementModal
          ticketId={selectedBooking.ticketId as any}
          onClose={() => {
            setShowRoomManagement(false);
            setSelectedBooking(null);
          }}
          onSuccess={handleRoomManagementSuccess}
        />
      )}
    </div>
  );
}

// Bulk Room Management Component
interface BulkRoomManagementProps {
  hotelId: Id<"hotels">;
}

function BulkRoomManagement({ hotelId }: BulkRoomManagementProps) {
  // State for single room creation
  const [roomNumber, setRoomNumber] = useState("");
  const [capacity, setCapacity] = useState(1);
  const [price, setPrice] = useState(0);

  // State for bulk room creation
  const [bulkPrefix, setBulkPrefix] = useState("Room ");
  const [bulkStartNumber, setBulkStartNumber] = useState(101);
  const [bulkNumberOfRooms, setBulkNumberOfRooms] = useState(5);
  const [bulkSuffix, setBulkSuffix] = useState("");
  const [bulkCapacity, setBulkCapacity] = useState(2);
  const [bulkPrice, setBulkPrice] = useState(100);
  const [previewRoomNames, setPreviewRoomNames] = useState<string[]>([]);
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false);

  const rooms = useQuery(api.hotels.getRoomsByHotel, { hotelId });
  const addRoom = useMutation(api.hotels.addRoom);
  const addRoomsInBulk = useMutation(api.hotels.addRoomsInBulk);

  const handleAddRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addRoom({ hotelId, roomNumber, capacity, price });
      toast.success("Room added successfully");
      setRoomNumber("");
      setCapacity(1);
      setPrice(0);
    } catch (error) {
      toast.error("Failed to add room");
    }
  };

  useEffect(() => {
    if (bulkNumberOfRooms > 0 && bulkNumberOfRooms <= 50) {
      const names = [];
      for (let i = 0; i < bulkNumberOfRooms; i++) {
        names.push(`${bulkPrefix}${bulkStartNumber + i}${bulkSuffix || ""}`);
      }
      setPreviewRoomNames(names);
    } else {
      setPreviewRoomNames([]);
    }
  }, [bulkPrefix, bulkStartNumber, bulkNumberOfRooms, bulkSuffix]);

  const handleBulkAddRooms = async (e: React.FormEvent) => {
    e.preventDefault();
    if (bulkNumberOfRooms <= 0) {
      toast.error("Number of rooms must be greater than 0.");
      return;
    }
    if (bulkNumberOfRooms > 50) {
      toast.error("Cannot create more than 50 rooms at once.");
      return;
    }
    setIsBulkSubmitting(true);
    try {
      const result = await addRoomsInBulk({
        hotelId,
        prefix: bulkPrefix,
        startNumber: bulkStartNumber,
        numberOfRooms: bulkNumberOfRooms,
        suffix: bulkSuffix,
        capacity: bulkCapacity,
        price: bulkPrice,
      });
      toast.success(result.message || `${result.count} rooms created successfully!`);
      // Reset bulk form fields
      setBulkPrefix("Room ");
      setBulkStartNumber(101);
      setBulkNumberOfRooms(5);
      setBulkSuffix("");
      setBulkCapacity(2);
      setBulkPrice(100);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to add rooms in bulk.");
    } finally {
      setIsBulkSubmitting(false);
    }
  };

  return (
    <div className="mt-4 pt-4 border-t space-y-6">
      {/* Single Room Creation Form */}
      <div className="p-4 border rounded-md bg-gray-50">
        <h5 className="font-semibold mb-3 text-md">Add Single Room</h5>
        <form onSubmit={handleAddRoom} className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
          <div className="md:col-span-2">
            <label htmlFor="roomNumber" className="block text-sm font-medium">Room Number</label>
            <input
              id="roomNumber"
              type="text"
              value={roomNumber}
              onChange={(e) => setRoomNumber(e.target.value)}
              className="w-full p-2 border rounded-md mt-1"
              required
            />
          </div>
          <div>
            <label htmlFor="capacity" className="block text-sm font-medium">Capacity</label>
            <input
              id="capacity"
              type="number"
              value={capacity}
              onChange={(e) => setCapacity(Number(e.target.value))}
              min="1"
              className="w-full p-2 border rounded-md mt-1"
              required
            />
          </div>
          <div>
            <label htmlFor="price" className="block text-sm font-medium">Price (GH₵)</label>
            <input
              id="price"
              type="number"
              value={price}
              onChange={(e) => setPrice(Number(e.target.value))}
              min="0"
              className="w-full p-2 border rounded-md mt-1"
              required
            />
          </div>
          <button
            type="submit"
            className="flex items-center justify-center bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 h-10"
          >
            <Bed className="w-4 h-4 mr-2" /> Add Room
          </button>
        </form>
      </div>

      {/* Bulk Room Creation Form */}
      <div className="p-4 border rounded-md bg-blue-50">
        <h5 className="font-semibold mb-3 text-md flex items-center">
          <Layers className="w-5 h-5 mr-2 text-blue-600" />
          Bulk Add Rooms (Pattern-Based)
        </h5>
        <form onSubmit={handleBulkAddRooms} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkPrefix" className="block text-sm font-medium">Prefix</label>
              <input
                id="bulkPrefix"
                type="text"
                value={bulkPrefix}
                onChange={(e) => setBulkPrefix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., Room, Suite"
              />
            </div>
            <div>
              <label htmlFor="bulkSuffix" className="block text-sm font-medium">Suffix</label>
              <input
                id="bulkSuffix"
                type="text"
                value={bulkSuffix}
                onChange={(e) => setBulkSuffix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., -A, -Standard"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkStartNumber" className="block text-sm font-medium">Start Number</label>
              <input
                id="bulkStartNumber"
                type="number"
                value={bulkStartNumber}
                onChange={(e) => setBulkStartNumber(Number(e.target.value))}
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkNumberOfRooms" className="block text-sm font-medium">Number of Rooms</label>
              <input
                id="bulkNumberOfRooms"
                type="number"
                value={bulkNumberOfRooms}
                onChange={(e) => setBulkNumberOfRooms(Number(e.target.value))}
                min="1"
                max="50"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkCapacity" className="block text-sm font-medium">Default Capacity (for all)</label>
              <input
                id="bulkCapacity"
                type="number"
                value={bulkCapacity}
                onChange={(e) => setBulkCapacity(Number(e.target.value))}
                min="1"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkPrice" className="block text-sm font-medium">Default Price (GH₵ for all)</label>
              <input
                id="bulkPrice"
                type="number"
                value={bulkPrice}
                onChange={(e) => setBulkPrice(Number(e.target.value))}
                min="0"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
          </div>

          {previewRoomNames.length > 0 && (
            <div className="mt-4">
              <h6 className="text-sm font-medium mb-1">Preview of Room Names:</h6>
              <div className="p-2 border rounded-md bg-white max-h-28 overflow-y-auto text-xs">
                {previewRoomNames.slice(0, 5).map(name => <div key={name}>{name}</div>)}
                {previewRoomNames.length > 5 && <div>...and {previewRoomNames.length - 5} more.</div>}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={isBulkSubmitting}
            className="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 w-full md:w-auto disabled:opacity-50"
          >
            <Layers className="w-4 h-4 mr-2" />
            {isBulkSubmitting ? "Adding..." : "Add Rooms in Bulk"}
          </button>
        </form>
      </div>

      {/* Existing Rooms List */}
      <div>
        <h5 className="font-semibold mb-2 text-md">Existing Rooms ({rooms?.length || 0})</h5>
        {rooms === undefined && <p>Loading rooms...</p>}
        {rooms && rooms.length === 0 && <p className="text-sm text-gray-500">No rooms added to this hotel yet.</p>}
        <div className="space-y-2">
          {rooms?.map((room) => (
            <div key={room._id} className="flex justify-between items-center p-3 border rounded-md bg-white shadow-sm">
              <div>
                <span className="font-semibold text-gray-700">{room.roomNumber}</span>
                <span className="text-sm text-gray-500 ml-4">
                  Capacity: {room.capacity}
                </span>
              </div>
              <span className="font-semibold text-gray-700">GH₵{room.price}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

