import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import TicketCard from "./TicketCard";
import { TicketType, RoomBasicInfo } from "../types"; // Assuming RoomBasicInfo is part of TicketType.roomDetails
import { useState } from "react";
import { SelectRoomForTicketModal } from "./SelectRoomForTicketModal"; // New Modal for room selection
import { PaymentModal } from "./PaymentModal"; // To handle payment for the selected room
import { Room } from "./RoomSelection"; // Full Room type for PaymentModal

export function MyTickets() {
  const tickets = useQuery(api.tickets.myTickets);
  const cancelTicket = useMutation(api.tickets.cancel);

  // State for the new room booking flow
  const [ticketForRoomSelection, setTicketForRoomSelection] = useState<TicketType | null>(null);
  const [selectedRoomForPayment, setSelectedRoomForPayment] = useState<Room | null>(null);

  // This function is called when the "Book Room" button on TicketCard is clicked
  const handleBookRoomRequest = (ticket: TicketType) => {
    if (!ticket.roomId) { // Only proceed if a room isn't already booked
      setTicketForRoomSelection(ticket);
    } else {
      // Optionally, implement a "View Room Details" or "Change Room" functionality here
      toast.info(`Room ${ticket.roomDetails?.roomNumber || ticket.roomDetails?.type || 'is already'} booked for this ticket.`);
    }
  };

  // This function is called from SelectRoomForTicketModal when a room is chosen
  const handleRoomSelectedForPayment = (room: Room) => {
    if (ticketForRoomSelection) {
      setSelectedRoomForPayment(room);
      // The PaymentModal will now open because both ticketForRoomSelection and selectedRoomForPayment are set
    }
  };

  const closeAndResetPaymentFlow = () => {
    setTicketForRoomSelection(null);
    setSelectedRoomForPayment(null);
  };

  const handlePaymentSuccessForRoom = () => {
    toast.success("Room booked and paid successfully!");
    closeAndResetPaymentFlow();
    // The ticket list will refresh due to Convex's reactivity, showing the updated room info.
  };
  const handleCancelTicket = async (ticketId: string) => {
    if (
      !confirm(
        "Are you sure you want to cancel this ticket? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await cancelTicket({ ticketId });
      toast.success("Ticket cancelled successfully");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to cancel ticket"
      );
    }
  };

  if (tickets === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const confirmedTickets = tickets.filter(
    (ticket) => ticket.status === "confirmed"
  );
  const cancelledTickets = tickets.filter(
    (ticket) => ticket.status === "cancelled"
  );

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          My Tickets ({confirmedTickets.length})
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {confirmedTickets.map((ticket) => (
            <TicketCard
              key={ticket._id}
              ticket={ticket}
              onCancel={handleCancelTicket}
              onBookRoomRequest={handleBookRoomRequest} // Updated prop name
            />
          ))}
        </div>

        {confirmedTickets.length === 0 && !ticketForRoomSelection && !selectedRoomForPayment && (
          <div className="text-center py-8 bg-white rounded-lg shadow-md">
            <p className="text-gray-500">You don't have any tickets yet.</p>
            <p className="text-gray-400">
              Browse events to purchase tickets!
            </p>
          </div>
        )}
      </div>

      {/* Modal for selecting a room for an existing ticket */}
      {ticketForRoomSelection && !selectedRoomForPayment && (
        <SelectRoomForTicketModal
          event={ticketForRoomSelection.event}
          onClose={() => setTicketForRoomSelection(null)}
          onRoomSelectedAndProceed={handleRoomSelectedForPayment}
        />
      )}

      {/* Payment Modal for the selected room */}
      {ticketForRoomSelection && selectedRoomForPayment && (
        <PaymentModal
          event={ticketForRoomSelection.event} // Event context
          quantity={1} // Assuming one room per ticket for this add-on flow
          selectedRoom={selectedRoomForPayment} // The room they just picked
          attendeeType={ticketForRoomSelection.attendeeType} // Attendee type from the ticket
          onClose={closeAndResetPaymentFlow}
          onSuccess={handlePaymentSuccessForRoom}
          paymentContext={{ // New prop to inform PaymentModal and backend
            type: "ADD_ROOM_TO_TICKET",
            ticketId: ticketForRoomSelection._id,
          }}
        />
      )}


      {cancelledTickets.length > 0 && (
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Cancelled Tickets ({cancelledTickets.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cancelledTickets.map((ticket) => (
              <TicketCard
                key={ticket._id}
                ticket={ticket}
                onCancel={handleCancelTicket}
                onBookRoomRequest={() => toast.info("Cannot book room for a cancelled ticket.")} // No action for cancelled
             />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
