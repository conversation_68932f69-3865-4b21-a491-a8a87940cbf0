import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, ChangeEvent, useEffect } from "react";
import { Id } from "../../convex/_generated/dataModel";
import { DateRange, DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";
import { addDays, differenceInDays, differenceInMonths, startOfDay, endOfDay, isAfter, isBefore } from "date-fns";

interface RoomSelectionProps {
  eventId: Id<"events">;
  onSelectRoom: (room: Room | null, extraDays: number) => void;
  attendeeType?: string;
}

export interface Hotel {
  _id: Id<"hotels">;
  name: string;
}

export interface Room {
  _id: Id<"rooms">;
  roomNumber: string;
  pricePerBedPerDay: number;
  capacity: number;
  hotelId: Id<"hotels">;
  type: string;
  availableCapacity?: number;
  totalOccupied?: number;
  hasAvailability?: boolean;
  totalPrice?: number;
}

export function RoomSelection({ eventId, onSelectRoom, attendeeType }: RoomSelectionProps) {
  const [selectedHotelId, setSelectedHotelId] = useState<Id<"hotels">>("");
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [extraDaysRange, setExtraDaysRange] = useState<DateRange | undefined>(undefined);
  const [extraDaysCount, setExtraDaysCount] = useState<number>(0);

  const hotels = useQuery(api.hotels.getAccommodationsByEvent, { eventId }) as Hotel[] | undefined;
  const rooms = useQuery(
    api.hotels.getAvailableRoomsByHotel,
    selectedHotelId ? { hotelId: selectedHotelId, attendeeType } : "skip"
  ) as Room[] | undefined;
  const event = useQuery(api.events.getById, { eventId });

  useEffect(() => {
    if (selectedRoom) {
      onSelectRoom(selectedRoom, extraDaysCount);
    } else {
      onSelectRoom(null, 0);
    }
  }, [selectedRoom, extraDaysCount, onSelectRoom]);

  const handleHotelChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const hotelId = e.target.value;
    setSelectedHotelId(hotelId as Id<"hotels">);
    setSelectedRoom(null); // Reset room selection when hotel changes
    setExtraDaysRange(undefined); // Reset extra days selection
    setExtraDaysCount(0);
  };

  const handleRoomSelection = (e: ChangeEvent<HTMLSelectElement>) => {
    const roomId = e.target.value;
    if (!roomId) {
      setSelectedRoom(null);
      return;
    }
    const room = rooms?.find((r) => r._id === roomId);
    setSelectedRoom(room || null);
  };

  const handleDayPickerSelect = (range: DateRange | undefined) => {
    setExtraDaysRange(range);
    if (range?.from && range?.to && event?.startDate && event?.endDate) {
      const eventStartDate = startOfDay(new Date(event.startDate));
      const eventEndDate = endOfDay(new Date(event.endDate));

      let count = 0;
      let currentDate = startOfDay(range.from);

      while (currentDate <= endOfDay(range.to)) {
        // Check if the current date is outside the event date range
        if (isBefore(currentDate, eventStartDate) || isAfter(currentDate, eventEndDate)) {
          count++;
        }
        currentDate = addDays(currentDate, 1);
      }
      setExtraDaysCount(count);
    } else {
      setExtraDaysCount(0);
    }
  };

  let disabledDays: Date[] = [];
  let defaultSelected: DateRange | undefined = undefined;
  let minDate: Date | undefined = undefined;
  let maxDate: Date | undefined = undefined;

  if (event?.startDate && event?.endDate) {
    const eventStartDate = startOfDay(new Date(event.startDate));
    const eventEndDate = endOfDay(new Date(event.endDate));

    // Calculate min and max dates for the 3-day window around the event
    minDate = addDays(eventStartDate, -3);
    maxDate = addDays(eventEndDate, 3);

    disabledDays = [
      { before: minDate },
      { after: maxDate },
      { from: eventStartDate, to: eventEndDate } // Disable event days
    ];

    defaultSelected = {
      from: eventStartDate,
      to: eventEndDate,
    };
  }

  return (
    <div className="mt-4 space-y-4">
      <div>
        <label className="block text-sm font-medium">Book a Room</label>
        <select
          value={selectedHotelId}
          onChange={handleHotelChange}
          className="w-full p-2 border rounded-md"
        >
          <option value="">Select accommodation</option>
          {hotels?.map((hotel) => (
            <option key={hotel._id} value={hotel._id}>
              {hotel.name}
            </option>
          ))}
        </select>
      </div>
      {selectedHotelId && (
        <div>
          <label className="block text-sm font-medium">Select a Room</label>
          <select
            onChange={handleRoomSelection}
            className="w-full p-2 border rounded-md"
            defaultValue=""
          >
            <option value="">Select a room</option>
            {rooms?.map((room: any) => (
              <option key={room._id} value={room._id}>
                {room.roomNumber} - GH₵{room.pricePerBedPerDay}/Bed/Day (Capacity: {room.capacity})
              </option>
            ))}
          </select>
        </div>
      )}
      {selectedRoom && event?.startDate && event?.endDate && (
        <div>
          <label className="block text-sm font-medium">Select Extra Accommodation Days</label>
          <DayPicker
            mode="range"
            selected={extraDaysRange}
            onSelect={handleDayPickerSelect}
            disabled={disabledDays}
            startMonth={minDate}
            endMonth={maxDate}
            numberOfMonths={Math.ceil(differenceInMonths(maxDate, minDate)) + 1}
          />
          {extraDaysCount > 0 && (
            <p className="text-sm text-gray-600 mt-2">
              Selected {extraDaysCount} extra day(s).
            </p>
          )}
        </div>
      )}
    </div>
  );
}