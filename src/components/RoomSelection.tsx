import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, ChangeEvent } from "react";
import { Id } from "../../convex/_generated/dataModel";

interface RoomSelectionProps {
  eventId: Id<"events">;
  onSelectRoom: (room: Room | null) => void;
}

export interface Hotel {
  _id: Id<"hotels">;
  name: string;
}

export interface Room {
  _id: Id<"rooms">;
  roomNumber: string;
  price: number;
  capacity: number;
  hotelId: Id<"hotels">;
  type: string;
  availableCapacity?: number;
  totalOccupied?: number;
  hasAvailability?: boolean;
}

export function RoomSelection({ eventId, onSelectRoom }: RoomSelectionProps) {
  const [selectedHotelId, setSelectedHotelId] = useState<Id<"hotels">>("");
  const hotels = useQuery(api.hotels.getHotelsByEvent, { eventId }) as Hotel[] | undefined;
  const rooms = useQuery(
    api.hotels.getAvailableRoomsByHotel,
    selectedHotelId ? { hotelId: selectedHotelId } : "skip"
  ) as Room[] | undefined;

  const handleHotelChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const hotelId = e.target.value;
    setSelectedHotelId(hotelId as Id<"hotels">);
    onSelectRoom(null); // Reset room selection when hotel changes
  };

  const handleRoomSelection = (e: ChangeEvent<HTMLSelectElement>) => {
    const roomId = e.target.value;
    if (!roomId) {
      onSelectRoom(null);
      return;
    }
    const room = rooms?.find((r) => r._id === roomId);
    if (room) {
      onSelectRoom(room);
    }
  };

  return (
    <div className="mt-4 space-y-4">
      <div>
        <label className="block text-sm font-medium">Book a Room</label>
        <select
          value={selectedHotelId}
          onChange={handleHotelChange}
          className="w-full p-2 border rounded-md"
        >
          <option value="">Select a hotel</option>
          {hotels?.map((hotel) => (
            <option key={hotel._id} value={hotel._id}>
              {hotel.name}
            </option>
          ))}
        </select>
      </div>
      {selectedHotelId && (
        <div>
          <label className="block text-sm font-medium">Select a Room</label>
          <select
            onChange={handleRoomSelection}
            className="w-full p-2 border rounded-md"
            defaultValue=""
          >
            <option value="">Select a room</option>
            {rooms?.map((room: any) => (
              <option key={room._id} value={room._id}>
                {room.roomNumber} - GH₵{room.price} ({room.availableCapacity}/{room.capacity} available)
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
}