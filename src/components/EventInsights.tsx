import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { TRANSACTION_CHARGE_RATE } from "../utils/constants";

interface EventInsightsProps {
  eventId: Id<"events">;
  onClose: () => void;
}

// Define a more specific type for attendee insights based on expected data
interface AttendeeInsight {
  _id: Id<"tickets">; // Assuming _id is the ticket ID
  userName?: string | null;
  userEmail: string;
  quantity: number;
  totalPrice: number; // This is the price *including* the transaction charge
  status: string;
  paymentStatus: string;
  purchaseDate: number; // Timestamp
  roomId?: Id<"rooms"> | string | null; // ID of the booked room, if any
  attendeeType?: string | null; // e.g., VIP, Delegate
  gender?: "Male" | "Female" | "Other" | string | null;
  commanderyAuxiliaryName?: string | null;
  grandName?: string | null;
  districtName?: string | null;
  rank?: string | null;
}

export function EventInsights({ eventId, onClose }: EventInsightsProps) {
  const insights = useQuery(api.analytics.getEventInsights, { eventId });

  if (insights === undefined) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto p-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!insights) return null;
  
  const event = insights.event;
  const summary = insights.summary;
  const timeline = insights.timeline;
  const attendees: AttendeeInsight[] = insights.attendees; // Apply the specific type

  // Calculate detailed net revenue breakdown
  let calculatedNetEventRevenue = 0;
  let calculatedNetHotelRevenue = 0;
  let totalRoomsBooked = 0;
  const confirmedPaidAttendees = attendees.filter(att => att.status === "confirmed" && att.paymentStatus === "success");

  confirmedPaidAttendees.forEach((attendee: any) => {
    const attendeeNetTotalPaid = attendee.totalPrice / (1 + TRANSACTION_CHARGE_RATE);
    const eventPortionNetForAttendee = (event.price || 0) * attendee.quantity;

    if (attendee.roomId) {
      const hotelPortionNetForAttendee = attendeeNetTotalPaid - eventPortionNetForAttendee;
      calculatedNetEventRevenue += eventPortionNetForAttendee;
      calculatedNetHotelRevenue += Math.max(0, hotelPortionNetForAttendee); // Ensure hotel revenue isn't negative
      totalRoomsBooked++; // Increment if a room is booked
    } else {
      calculatedNetEventRevenue += attendeeNetTotalPaid; // All of it is event revenue
    }
  });

  const netTotalRevenue = calculatedNetEventRevenue + calculatedNetHotelRevenue;
  const avgNetEventCost = confirmedPaidAttendees.length > 0 ? calculatedNetEventRevenue / confirmedPaidAttendees.length : 0;

  // Calculate Registrant Summary
  const totalRegistrationsCount = confirmedPaidAttendees.reduce((sum, att) => sum + att.quantity, 0);
  const totalKnights = confirmedPaidAttendees
    .filter(att => att.gender === "Male")
    .reduce((sum, att) => sum + att.quantity, 0);
  const totalLadies = confirmedPaidAttendees
    .filter(att => att.gender === "Female")
    .reduce((sum, att) => sum + att.quantity, 0);

  const uniqueCommanderies = new Set(
    confirmedPaidAttendees.map(att => att.commanderyAuxiliaryName).filter(Boolean)
  );
  const totalCommanderiesRegistered = uniqueCommanderies.size;

  const byGrands = confirmedPaidAttendees.reduce<Record<string, number>>((acc, att) => {
    if (att.grandName) {
      acc[att.grandName] = (acc[att.grandName] || 0) + att.quantity;
    }
    return acc;
  }, {});

  const byDistricts = confirmedPaidAttendees.reduce<Record<string, number>>((acc, att) => {
    if (att.districtName) {
      acc[att.districtName] = (acc[att.districtName] || 0) + att.quantity;
    }
    return acc;
  }, {});

  const byRanks = confirmedPaidAttendees.reduce<Record<string, number>>((acc, att) => {
    if (att.rank) {
      acc[att.rank] = (acc[att.rank] || 0) + att.quantity;
    }
    return acc;
  }, {});

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{event.title} - Insights</h2>
              <p className="text-gray-600 mt-1">Detailed analytics and performance metrics</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-800 mb-1">Total Net Revenue</div>
              <div className="text-2xl font-bold text-blue-600 mb-2">
                GH₵{netTotalRevenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className="text-xs text-blue-700">
                Event: GH₵{calculatedNetEventRevenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className="text-xs text-blue-700">
                Hotel: GH₵{calculatedNetHotelRevenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{summary.totalAttendees}</div>
              <div className="text-sm text-green-800">Total Attendees</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600" title="Based on total capacity and confirmed attendees">
                {summary.occupancyRate.toFixed(1)}%
              </div>
              <div className="text-sm text-purple-800">Net Occupancy Rate</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {totalRoomsBooked}
              </div>
              <div className="text-sm text-orange-800">Rooms Booked</div>
            </div>
          </div>

          {/* Additional Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Registration Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-green-600">Confirmed:</span>
                  <span className="font-medium">{summary.confirmedTickets}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-yellow-600">Pending:</span>
                  <span className="font-medium">{summary.pendingTickets}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-red-600">Cancelled:</span>
                  <span className="font-medium">{summary.cancelledTickets}</span>
                </div>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Capacity</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Capacity:</span>
                  <span className="font-medium">{event.totalTickets}</span>
                </div>
                <div className="flex justify-between">
                  <span>Sold:</span>
                  <span className="font-medium">{event.soldTickets}</span>
                </div>
                <div className="flex justify-between">
                  <span>Available:</span>
                  <span className="font-medium">{summary.availableTickets}</span>
                </div>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Event Details</h3>
              <div className="space-y-2 text-sm">
                <div>📅 {new Date(event.startDate).toLocaleDateString()}</div>
                <div>📍 {event.location}</div>
                <div>🏷️ {event.category}</div>
              </div>
            </div>
          </div>

          {/* Registration Timeline */}
          {timeline.registrations.length > 0 && false && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Registration Timeline (Last 30 Days)</h3>
              <div className="bg-white border rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-700 mb-3">Daily Registrations</h4>
                    <div className="space-y-2">
                      {timeline.registrations.slice(-7).map((item: any) => (
                        <div key={item.date} className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">
                            {new Date(item.date).toLocaleDateString()}
                          </span>
                          <div className="flex items-center gap-2">
                            <div 
                              className="bg-blue-200 h-2 rounded"
                              style={{ width: `${(item.registrations / Math.max(...timeline.registrations.map((r: any) => r.registrations))) * 100}px` }}
                            ></div>
                            <span className="text-sm font-medium">{item.registrations}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-700 mb-3">Daily Revenue</h4>
                    <div className="space-y-2">
                      {timeline.revenue.slice(-7).map((item: any) => (
                        <div key={item.date} className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">
                            {new Date(item.date).toLocaleDateString()}
                          </span>
                          <div className="flex items-center gap-2">
                            <div 
                              className="bg-green-200 h-2 rounded"
                              style={{ width: `${(item.revenue / Math.max(...timeline.revenue.map((r: any) => r.revenue))) * 100}px` }}
                            ></div>
                            <span className="text-sm font-medium">GH₵{(item.revenue / (1 + TRANSACTION_CHARGE_RATE)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Exempted Members List */}
          {event.exemptedEmails && event.exemptedEmails.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Exempted Emails ({event.exemptedEmails.length})
              </h3>
              <div className="bg-white border rounded-lg p-4">
                <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                  {event.exemptedEmails.map((email, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {email}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Summary of Event Registrants */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary of Event Registrants</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-2">Overall</h4>
                <div className="space-y-1 text-sm">
                  <div>Total Registrations: <span className="font-medium">{totalRegistrationsCount}</span></div>
                  <div>Total Knights (Male): <span className="font-medium">{totalKnights}</span></div>
                  <div>Total Ladies (Female): <span className="font-medium">{totalLadies}</span></div>
                  <div>Total Commanderies/Auxiliaries: <span className="font-medium">{totalCommanderiesRegistered}</span></div>
                </div>
              </div>

              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-2">By Grands</h4>
                {Object.keys(byGrands).length > 0 ? (
                  <div className="space-y-1 text-sm max-h-40 overflow-y-auto">
                    {Object.entries(byGrands).map(([grand, count]) => (
                      <div key={grand} className="flex justify-between">
                        <span>{grand}:</span> <span className="font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                ) : <p className="text-sm text-gray-500">No Grand data available.</p>}
              </div>

              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-2">By Districts</h4>
                {Object.keys(byDistricts).length > 0 ? (
                  <div className="space-y-1 text-sm max-h-40 overflow-y-auto">
                    {Object.entries(byDistricts).map(([district, count]) => (
                      <div key={district} className="flex justify-between">
                        <span>{district}:</span> <span className="font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                ) : <p className="text-sm text-gray-500">No District data available.</p>}
              </div>
              
              <div className="bg-white border rounded-lg p-4 md:col-span-2 lg:col-span-3">
                <h4 className="font-semibold text-gray-700 mb-2">By Ranks (Officers)</h4>
                {Object.keys(byRanks).length > 0 ? (
                  <div className="space-y-1 text-sm max-h-40 overflow-y-auto">
                     <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4">
                      {Object.entries(byRanks)
                        .sort(([, countA], [, countB]) => countB - countA) // Sort by count desc
                        .map(([rank, count]) => (
                        <div key={rank} className="flex justify-between py-0.5">
                          <span className="truncate pr-2">{rank}:</span> <span className="font-medium flex-shrink-0">{count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : <p className="text-sm text-gray-500">No Rank data available.</p>}
              </div>
            </div>
          </div>
          {/* END Summary of Event Registrants */}

          {/* Attendees List */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirmed Attendees ({
                attendees.filter(
                  (a) => a.status === "confirmed" && a.paymentStatus === "success"
                ).length
              })
            </h3>
            <div className="bg-white border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tickets</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Attendee Type</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Remarks</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {attendees
                      .filter(attendee => attendee.status === "confirmed" && attendee.paymentStatus === "success")
                      .map((attendee: any) => (
                      <tr key={attendee._id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">{attendee.userName}</td>
                        <td className="px-4 py-3 text-sm text-gray-600">{attendee.userEmail}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">{attendee.quantity}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          GH₵{(attendee.totalPrice / (1 + TRANSACTION_CHARGE_RATE)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">{attendee.attendeeType}</td>
                        <td className="px-4 py-3 text-sm text-gray-600">{attendee.roomId ? "Event + Hotel" : "Event Only"}</td>
                        <td className="px-4 py-3">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Confirmed
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {new Date(attendee.purchaseDate).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {attendees.filter(
                  (a) =>
                    a.status === "confirmed" &&
                    a.paymentStatus === "success"
                ).length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No confirmed attendees yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
