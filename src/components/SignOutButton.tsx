"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { LogOut } from "lucide-react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
      className="w-full group flex items-center p-3 rounded-xl text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 backdrop-blur-sm border border-transparent hover:border-white/10 transition-all duration-300 transform hover:scale-[1.02]"
      onClick={() => void signOut()}
    >
      <LogOut className="mr-3 h-5 w-5 text-white/50 group-hover:text-white/80 transition-colors" />
      Sign out
    </button>
  );
}