"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { LogOut } from "lucide-react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
      className="w-full group flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/50 hover:transform hover:scale-[1.01] transition-all duration-200"
      onClick={() => void signOut()}
    >
      <LogOut className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-200 transition-colors" />
      Sign out
    </button>
  );
}