"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { LogOut } from "lucide-react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
      className="w-full flex items-center p-3 rounded-xl text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 transition-all duration-300 group border border-transparent hover:border-white/20 backdrop-blur-sm"
      onClick={() => void signOut()}
    >
      <div className="w-8 h-8 rounded-lg flex items-center justify-center mr-3 bg-gradient-to-r from-red-500 to-pink-500 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
        <LogOut className="w-4 h-4 text-white" />
      </div>
      <span className="flex-1 text-left">Sign out</span>

      {/* Hover effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
    </button>
  );
}