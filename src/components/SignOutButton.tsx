"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { LogOut } from "lucide-react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
      className="w-full flex items-center p-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white"
      onClick={() => void signOut()}
    >
      <LogOut className="mr-3 h-5 w-5" />
      Sign out
    </button>
  );
}