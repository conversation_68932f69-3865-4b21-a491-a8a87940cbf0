import React from 'react';
import { FileWithPath } from 'react-dropzone';
import { ImageDropzone } from './ImageDropzone';
import { CsvDropzone } from './CsvDropzone';

interface FormDataShape {
  title: string;
  description: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  location: string;
  price: number | string; // Can be string from input
  totalTickets: number | string; // Can be string from input
  category: string;
  level: string;
  imageUrl: string;
  registrationOpenDate: string;
  registrationCloseDate: string;
  attendees: string[]; // For allowed attendee types
  // Add any other common fields
}

interface EventFormFieldsProps {
  formData: FormDataShape;
  onFormChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  
  // For ImageDropzone
  featuredImageFile: FileWithPath | null;
  onFeaturedImageDrop: (file: FileWithPath | null) => void;
  currentEventImageUrl?: string | null;
  imageDropzoneId: string;

  // For CsvDropzone
  exemptedEmailsFile: FileWithPath | null;
  onExemptedEmailsDrop: (file: FileWithPath | null) => void;
  onExemptedEmailsParsed: (emails: string[]) => void;
  parsedEmailsCount: number;
  parseEmailsCSV: (file: File) => Promise<string[]>;
  csvDropzoneId: string;

  // For displaying/editing exempted emails in edit mode
  isEditMode?: boolean;
  editableExemptedEmails?: string[];
  onRemoveExemptedEmail?: (index: number) => void;

  // Constants for dropdowns/checkboxes
  eventLevels: string[];
  allowedAttendeeTypes: string[];
  categories: string[];
}

export function EventFormFields({
  formData,
  onFormChange,
  featuredImageFile,
  onFeaturedImageDrop,
  currentEventImageUrl,
  imageDropzoneId,
  exemptedEmailsFile,
  onExemptedEmailsDrop,
  onExemptedEmailsParsed,
  parsedEmailsCount,
  parseEmailsCSV,
  csvDropzoneId,
  isEditMode = false,
  editableExemptedEmails = [],
  onRemoveExemptedEmail,
  eventLevels,
  allowedAttendeeTypes,
  categories,
}: EventFormFieldsProps) {
  return (
    <>
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
          Event Title *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={onFormChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter event title"
          required
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={onFormChange}
          rows={4}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Describe your event"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">Start Date *</label>
          <input type="date" id="startDate" name="startDate" value={formData.startDate} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
        <div>
          <label htmlFor="startTime" className="block text-sm font-medium text-gray-700 mb-2">Start Time *</label>
          <input type="time" id="startTime" name="startTime" value={formData.startTime} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">End Date *</label>
          <input type="date" id="endDate" name="endDate" value={formData.endDate} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
        <div>
          <label htmlFor="endTime" className="block text-sm font-medium text-gray-700 mb-2">End Time *</label>
          <input type="time" id="endTime" name="endTime" value={formData.endTime} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
      </div>

      <div>
        <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">Event Location Details *</label>
        <textarea id="location" name="location" value={formData.location} onChange={onFormChange} rows={3} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Full address and any specific directions" required />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <select name="category" id="category" value={formData.category} onChange={onFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
          </select>
        </div>
        <div>
          <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-2">Event Level *</label>
          <select id="level" name="level" value={formData.level} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            {eventLevels.map((level) => (<option key={level} value={level}>{level}</option>))}
          </select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">Event Cost (GH₵) *</label>
          <input type="number" id="price" name="price" value={formData.price} onChange={onFormChange} min="0" step="0.01" className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00 for free event" required />
        </div>
        <div>
          <label htmlFor="totalTickets" className="block text-sm font-medium text-gray-700 mb-2">Total Tickets *</label>
          <input type="number" id="totalTickets" name="totalTickets" value={formData.totalTickets} onChange={onFormChange} min="0" className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter the total number of tickets" required />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Allowed Attendee Types *</label>
        <div className="space-y-2">
          {allowedAttendeeTypes.map(type => (
            <label key={type} className="flex items-center">
              <input
                type="checkbox"
                name="attendees" // This name is used in CreateEvent's handleChange
                value={type}
                checked={formData.attendees.includes(type)}
                onChange={onFormChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">{type}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="registrationOpenDate" className="block text-sm font-medium text-gray-700 mb-2">Registration Open Date *</label>
          <input type="date" id="registrationOpenDate" name="registrationOpenDate" value={formData.registrationOpenDate} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
        <div>
          <label htmlFor="registrationCloseDate" className="block text-sm font-medium text-gray-700 mb-2">Registration Close Date *</label>
          <input type="date" id="registrationCloseDate" name="registrationCloseDate" value={formData.registrationCloseDate} onChange={onFormChange} className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">Event Image URL (or upload below)</label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">https://</span>
            <input type="url" name="imageUrl" id="imageUrl" value={formData.imageUrl} onChange={onFormChange} className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300" placeholder="example.com/image.jpg" />
          </div>
        </div>

        <ImageDropzone
          onDrop={onFeaturedImageDrop}
          currentFile={featuredImageFile}
          currentImageUrl={currentEventImageUrl}
          inputPropsId={imageDropzoneId}
        />

        <CsvDropzone
          onDrop={onExemptedEmailsDrop}
          onParsed={onExemptedEmailsParsed}
          currentFile={exemptedEmailsFile}
          parsedItemsCount={parsedEmailsCount}
          parseCsvFile={parseEmailsCSV}
          inputPropsId={csvDropzoneId}
        />

        {isEditMode && editableExemptedEmails.length > 0 && onRemoveExemptedEmail && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Currently Exempted Emails ({editableExemptedEmails.length})
            </label>
            <div className="mt-1 p-3 border border-gray-200 rounded-md max-h-40 overflow-y-auto bg-gray-50 flex flex-wrap gap-2">
              {editableExemptedEmails.map((email, index) => (
                <span 
                  key={index} 
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {email}
                  <button
                    type="button"
                    onClick={() => onRemoveExemptedEmail(index)}
                    className="ml-1.5 flex-shrink-0 inline-flex items-center justify-center h-4 w-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white"
                    aria-label={`Remove ${email}`}
                  >
                    <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                      <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
}