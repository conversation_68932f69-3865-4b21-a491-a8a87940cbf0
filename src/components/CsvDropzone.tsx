import { useCallback } from 'react';
import { useDropzone, FileWithPath } from 'react-dropzone';
import { toast } from 'sonner';

interface CsvDropzoneProps {
  onDrop: (file: FileWithPath | null) => void;
  onParsed: (emails: string[]) => void;
  currentFile: FileWithPath | null;
  parsedEmailsCount: number;
  parseEmailsCSV: (file: File) => Promise<string[]>;
  inputPropsId?: string; // For unique IDs
}

export function CsvDropzone({ onDrop, onParsed, currentFile, parsedEmailsCount, parseEmailsCSV, inputPropsId = "exempted-emails-csv-upload" }: CsvDropzoneProps) {
  const onDropCallback = useCallback(async (acceptedFiles: FileWithPath[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      if (file.name.endsWith('.csv') || file.type === 'text/csv') {
        onDrop(file);
        try {
          const emails = await parseEmailsCSV(file);
          onParsed(emails);
          toast.info(`${emails.length} emails parsed from ${file.name}.`);
        } catch (error) {
          toast.error(error instanceof Error ? error.message : 'Failed to parse CSV file');
          onParsed([]);
        }
      } else {
        toast.error('Please upload a CSV file');
        onDrop(null);
        onParsed([]);
      }
    } else {
      onDrop(null);
      onParsed([]);
    }
  }, [onDrop, onParsed, parseEmailsCSV]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onDropCallback,
    accept: { 'text/csv': ['.csv'] },
    multiple: false,
  });

  const displayFileName = currentFile?.name;
  const displayFileSize = currentFile ? `${(currentFile.size / 1024).toFixed(1)} KB` : '';

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Exempted Emails (CSV)
      </label>
      <div {...getRootProps()} className={`mt-1 flex flex-col items-center justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md transition-colors ${ isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400' }`} >
        <div className="space-y-1 text-center">
          <svg className={`mx-auto h-12 w-12 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true" >
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          <div className="flex text-sm text-gray-600 justify-center">
            <label htmlFor={inputPropsId} className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500" >
              <span>Upload a CSV</span>
              <input {...getInputProps()} id={inputPropsId} name="exemptedEmailsCsv" />
            </label>
            <p className="pl-1">or drag and drop</p>
          </div>
          <p className="text-xs text-gray-500">CSV up to 10MB</p>
        </div>
        {displayFileName && ( <div className="mt-3 text-sm text-gray-700 text-center"> <div className="font-medium">Selected: {displayFileName}</div> <div className="text-xs text-gray-500"> {parsedEmailsCount} emails parsed • {displayFileSize} </div> </div> )}
      </div>
      <p className="mt-1 text-xs text-gray-500">Upload a CSV file with one email per line (no header).</p>
    </div>
  );
}