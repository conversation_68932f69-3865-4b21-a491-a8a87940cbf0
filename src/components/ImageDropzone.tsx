import { useCallback } from 'react';
import { useDropzone, FileWithPath } from 'react-dropzone';
import { toast } from 'sonner';

interface ImageDropzoneProps {
  onDrop: (file: FileWithPath | null) => void;
  currentFile: FileWithPath | null;
  currentImageUrl?: string | null; // To display existing image URL if no new file is selected
  inputPropsId?: string; // For unique IDs if multiple dropzones on a page
}

export function ImageDropzone({ onDrop, currentFile, currentImageUrl, inputPropsId = "featured-image-upload" }: ImageDropzoneProps) {
  const onDropCallback = useCallback((acceptedFiles: FileWithPath[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      if (file.type.startsWith('image/')) {
        onDrop(file);
      } else {
        toast.error('Please upload an image file (PNG, JPG, GIF)');
        onDrop(null); // Explicitly pass null if type is wrong
      }
    } else {
       onDrop(null); // Pass null if no files accepted (e.g., drag cancelled)
    }
  }, [onDrop]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onDropCallback,
    accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.gif'] },
    multiple: false,
  });

  const displayFileName = currentFile?.name;
  const displayFileSize = currentFile ? `${(currentFile.size / 1024).toFixed(1)} KB` : '';
  const displayFileType = currentFile?.type;

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Event Featured Image
      </label>
      <div
        {...getRootProps()}
        className={`mt-1 flex flex-col items-center justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md transition-colors ${
          isDragActive
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <div className="space-y-1 text-center">
          <svg className={`mx-auto h-12 w-12 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true" >
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          <div className="flex text-sm text-gray-600 justify-center">
            <label htmlFor={inputPropsId} className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500" >
              <span>Upload an image</span>
              <input {...getInputProps()} id={inputPropsId} name="featuredImage" />
            </label>
            <p className="pl-1">or drag and drop</p>
          </div>
          <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
        </div>
        {displayFileName && (
          <div className="mt-3 text-sm text-gray-700 text-center">
            <div className="font-medium">Selected: {displayFileName}</div>
            <div className="text-xs text-gray-500"> {displayFileSize} • {displayFileType} </div>
          </div>
        )}
        {!displayFileName && currentImageUrl && ( <div className="mt-3 text-sm text-gray-700 text-center"> <p className="font-medium">Current image:</p> <img src={currentImageUrl} alt="Current event" className="max-h-20 mx-auto mt-1 rounded" /> </div> )}
      </div>
    </div>
  );
}