import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import { PaymentModal } from "./PaymentModal";
import EventCard from "./EventCard";
import { EventType } from "../types";
import { RoomSelection, Room } from "./RoomSelection";

const categories = [
  "All",
  "National",
  "District",
  "Grand",
  "Local",
];

export function EventList() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedEvent, setSelectedEvent] = useState<EventType | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedQuantity, setSelectedQuantity] = useState(1);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [selectedAttendeeType, setSelectedAttendeeType] = useState<string>("");

  const events = useQuery(api.events.list, {
    category: selectedCategory === "All" ? undefined : selectedCategory,
  });

  // Get user's tickets to check for existing registrations
  const userTickets = useQuery(api.tickets.myTickets);

  const recordEventView = useMutation(api.analytics.recordEventView);

  // Helper function to check if user is already registered for an event
  const isUserRegistered = (eventId: string) => {
    if (!userTickets) return false;
    return userTickets.some(ticket =>
      ticket.event._id === eventId && ticket.status !== "cancelled"
    );
  };

  const handleEventSelect = (event: EventType) => {
    setSelectedEvent(event);
    recordEventView({ eventId: event._id as any });
  };

  const handleRegisterClick = (quantity: number, room?: Room | null, attendeeType?: string) => {
    setSelectedQuantity(quantity);
    setSelectedRoom(room || null);
    setSelectedAttendeeType(attendeeType || "");
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setSelectedEvent(null);
    toast.success("Successfully registered for the event!");
  };

  if (events === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Filter by Category</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === category
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {events.map((event) => (
          <EventCard
            key={event._id}
            event={event}
            onSelect={handleEventSelect}
            isUserRegistered={isUserRegistered(event._id)}
          />
        ))}
      </div>

      {events.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No events found in this category.</p>
        </div>
      )}

      {selectedEvent && (
        <EventModal
          event={selectedEvent}
          onClose={() => setSelectedEvent(null)}
          onRegister={handleRegisterClick}
          isUserRegistered={isUserRegistered(selectedEvent._id)}
        />
      )}

      {showPaymentModal && selectedEvent && (
        <PaymentModal
          event={selectedEvent}
          quantity={selectedQuantity}
          selectedRoom={selectedRoom}
          attendeeType={selectedAttendeeType}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}

function EventModal({
  event,
  onClose,
  onRegister,
  isUserRegistered,
}: {
  event: EventType;
  onClose: () => void;
  onRegister: (quantity: number, room?: Room | null, attendeeType?: string) => void;
  isUserRegistered: boolean;
}) {
  const [attendeeType, setAttendeeType] = useState<string>(event.allowedAttendeeTypes?.[0] || "");
  const quantity = 1; // Hardcode quantity to 1
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const availableTickets = event.totalTickets - (event.soldTickets || 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-2xl font-bold text-gray-900">{event.title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {event.imageUrl && (
            <img
              src={event.imageUrl}
              alt={event.title}
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
          )}

          <div className="space-y-4 mb-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600">{event.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900">Event Date & Time</h4>
                <p className="text-gray-600">
                  {new Date(event.startDate).toLocaleString()}
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Location</h4>
                <p className="text-gray-600">{event.location}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Organizer</h4>
                <p className="text-gray-600">{event.organizerName}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Category</h4>
                <p className="text-gray-600">{event.category}</p>
              </div>
            </div>
          </div>

          {availableTickets > 0 ? (
            <div className="border-t pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-3xl font-bold text-blue-600">
                      {event.price === 0 ? "Free" : `GH₵${event.price.toLocaleString()}`}
                    </span>
                    {event.price > 0 && <span className="text-gray-500 ml-2">per person</span>}
                  </div>
                  <div className="text-sm text-gray-500">
                    {availableTickets} spots available
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-1 gap-4"> {/* Changed to 1 column if only attendee type is left */}
                  {event.allowedAttendeeTypes && event.allowedAttendeeTypes.length > 0 && (
                    <div>
                      <label htmlFor="attendeeType" className="text-sm font-medium block mb-1">
                        Registering as:
                      </label>
                      <select
                        id="attendeeType"
                        value={attendeeType}
                        onChange={(e) => setAttendeeType(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2"
                      >
                        {event.allowedAttendeeTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold">
                    Event Cost: {event.price === 0 ? "Free" : `GH₵${(event.price * quantity).toLocaleString()}`}
                  </div>
                </div>
                {!isUserRegistered && (
                  <RoomSelection eventId={event._id as any} onSelectRoom={setSelectedRoom} />
                )}

                {isUserRegistered ? (
                  <div className="text-center">
                    <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                      <p className="text-green-800 font-medium">✓ You are already registered for this event</p>
                      <p className="text-green-600 text-sm mt-1">Check your tickets to manage your booking</p>
                    </div>
                    <button
                      onClick={onClose}
                      className="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition-colors"
                    >
                      Close
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => onRegister(quantity, selectedRoom, attendeeType)}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    {event.price === 0 ? "Register for Free Event" : `Register & Pay GH₵${(event.price * quantity + (selectedRoom?.price || 0)).toLocaleString()}`}
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="border-t pt-6 text-center">
              <p className="text-red-600 font-semibold text-lg">Event is Full</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
