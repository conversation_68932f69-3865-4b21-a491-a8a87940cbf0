import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import { PaymentModal } from "./PaymentModal";
import EventCard from "./EventCard";
import { EventType } from "../types";
import { RoomSelection, Room } from "./RoomSelection";
import { RoomVisibilityDebugger } from "./RoomVisibilityDebugger";
import { List, Grid, Calendar as CalendarIcon, MapPin, Users, Clock, CheckCircle } from "lucide-react";
import { format, isPast, isToday, isTomorrow } from 'date-fns';

const categories = [
  "All",
  "National",
  "District",
  "Grand",
  "Local",
];

export function EventList() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedEvent, setSelectedEvent] = useState<EventType | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedQuantity, setSelectedQuantity] = useState(1);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [selectedAttendeeType, setSelectedAttendeeType] = useState<string>("");
  const [extraDays, setExtraDays] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const events = useQuery(api.events.list, {
    category: selectedCategory === "All" ? undefined : selectedCategory,
  });

  // Get user's tickets to check for existing registrations
  const userTickets = useQuery(api.tickets.myTickets);

  const recordEventView = useMutation(api.analytics.recordEventView);

  // Helper function to check if user is already registered for an event
  const isUserRegistered = (eventId: string) => {
    if (!userTickets) return false;
    return userTickets.some(ticket =>
      ticket.event._id === eventId && ticket.status !== "cancelled"
    );
  };

  // Helper function to get user's attendee type for an event
  const getUserAttendeeType = (eventId: string) => {
    if (!userTickets) return null;
    const ticket = userTickets.find(ticket =>
      ticket.event?._id === eventId && ticket.status !== "cancelled"
    );
    return ticket?.attendeeType || null;
  };

  const handleEventSelect = (event: EventType) => {
    setSelectedEvent(event);
    recordEventView({ eventId: event._id as any });
  };

  const handleRegisterClick = (quantity: number, room?: Room | null, attendeeType?: string, extraDays?: number) => {
    setSelectedQuantity(quantity);
    setSelectedRoom(room || null);
    setSelectedAttendeeType(attendeeType || "");
    setExtraDays(extraDays || 0);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setSelectedEvent(null);
    toast.success("Successfully registered for the event!");
  };

  if (events === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === category
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              {category}
            </button>
          ))}
        </div>
        <div className="flex space-x-2 ml-auto">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'
              }`}
            aria-label="Grid view"
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'
              }`}
            aria-label="List view"
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {events?.map((event) => (
            <EventCard
              key={event._id}
              event={event}
              onSelect={handleEventSelect}
              isUserRegistered={isUserRegistered(event._id)}
              compact={true}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {events?.map((event) => (
            <EventCard
              key={event._id}
              event={event}
              onSelect={handleEventSelect}
              isUserRegistered={isUserRegistered(event._id)}
              compact={false}
            />
          ))}
        </div>
      )}

      {events.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No events found in this category.</p>
        </div>
      )}

      {selectedEvent && (
        <EventModal
          event={selectedEvent}
          onClose={() => setSelectedEvent(null)}
          onRegister={handleRegisterClick}
          isUserRegistered={isUserRegistered(selectedEvent._id)}
          userAttendeeType={getUserAttendeeType(selectedEvent._id)}
        />
      )}

      {showPaymentModal && selectedEvent && (
        <PaymentModal
          event={selectedEvent}
          quantity={selectedQuantity}
          selectedRoom={selectedRoom}
          attendeeType={selectedAttendeeType}
          extraDays={extraDays}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}

function EventModal({
  event,
  onClose,
  onRegister,
  isUserRegistered,
  userAttendeeType,
}: {
  event: EventType;
  onClose: () => void;
  onRegister: (quantity: number, room?: Room | null, attendeeType?: string, extraDays?: number) => void;
  isUserRegistered: boolean;
  userAttendeeType: string | null;
}) {
  const [attendeeType, setAttendeeType] = useState<string>(event.allowedAttendeeTypes?.[0] || "");
  const quantity = 1; // Hardcode quantity to 1
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [selectedExtraDays, setSelectedExtraDays] = useState<number>(0);

  const handleRoomSelection = (room: Room | null, extraDays: number) => {
    setSelectedRoom(room);
    setSelectedExtraDays(extraDays);
  };

  const availableTickets = event.totalTickets - (event.soldTickets || 0);

  const eventDurationInDays = useMemo(() => {
    if (!event.startDate || !event.endDate) return 1; // Default to 1 day if dates are not set
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 1;
  }, [event.startDate, event.endDate]);

  const totalPrice = useMemo(() => {
    let currentPrice = event.price * quantity;

    if (selectedRoom) {
      const baseRoomPrice = eventDurationInDays * selectedRoom.pricePerBedPerDay;
      let extraDaysCost = 0;

      if (selectedExtraDays > 0) {
        // Calculate extra days cost, ensuring it's only for days outside the event duration
        extraDaysCost = selectedExtraDays * selectedRoom.pricePerBedPerDay * selectedRoom.capacity;
      }
      currentPrice += baseRoomPrice + extraDaysCost;
    }

    return currentPrice;
  }, [event.price, quantity, selectedRoom, eventDurationInDays, selectedExtraDays]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-2xl font-bold text-gray-900">{event.title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {event.imageUrl && (
            <img
              src={event.imageUrl}
              alt={event.title}
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
          )}

          <div className="space-y-4 mb-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600">{event.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900">Event Date & Time</h4>
                <p className="text-gray-600">
                  {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Location</h4>
                <p className="text-gray-600">{event.location}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Organizer</h4>
                <p className="text-gray-600">{event.organizerName}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Category</h4>
                <p className="text-gray-600">{event.category}</p>
              </div>
            </div>
          </div>

          {availableTickets > 0 ? (
            <div className="border-t pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-3xl font-bold text-blue-600">
                      {event.price === 0 ? "Free" : `GH₵${event.price.toLocaleString()}`}
                    </span>
                    {event.price > 0 && <span className="text-gray-500 ml-2">per person</span>}
                  </div>
                  <div className="text-sm text-gray-500">
                    {availableTickets} spots available
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                  {isUserRegistered ? (
                    // Show registration status for already registered users
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <p className="text-blue-800 font-medium">
                        You are already registered as {userAttendeeType ? `a${['A', 'E', 'I', 'O', 'U'].includes(userAttendeeType[0]) ? 'n' : ''} ${userAttendeeType}` : 'an attendee'} for this event.
                      </p>
                      <p className="text-blue-600 text-sm mt-1">
                        Check your tickets to manage your booking or add accommodation.
                      </p>
                    </div>
                  ) : (
                    // Show attendee type selection for new registrations
                    event.allowedAttendeeTypes && event.allowedAttendeeTypes.length > 0 && (
                      <div>
                        <label htmlFor="attendeeType" className="text-sm font-medium block mb-1">
                          Registering as:
                        </label>
                        <select
                          id="attendeeType"
                          value={attendeeType}
                          onChange={(e) => setAttendeeType(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2"
                        >
                          {event.allowedAttendeeTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>
                    )
                  )}
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold">
                    Event Cost: {event.price === 0 ? "Free" : `GH₵${(totalPrice).toLocaleString()}`}
                  </div>
                </div>
                {!isUserRegistered && (
                  <>
                    <RoomSelection eventId={event._id as any} onSelectRoom={handleRoomSelection} attendeeType={attendeeType} />
                    {/* Show debugger only in development/staging environments */}
                    {import.meta.env.VITE_NODE_ENV !== 'production' && (
                      <RoomVisibilityDebugger eventId={event._id as any} attendeeType={attendeeType} />
                    )}
                  </>
                )}

                {!isUserRegistered && (
                  <button
                    onClick={() => onRegister(quantity, selectedRoom, attendeeType, selectedExtraDays)}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    {event.price === 0 ? "Register for Free Event" : `Register & Pay GH₵${(totalPrice).toLocaleString()}`}
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="border-t pt-6 text-center">
              <p className="text-red-600 font-semibold text-lg">Event is Full</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
