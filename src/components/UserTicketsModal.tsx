import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface UserTicketsModalProps {
  userId: Id<"users">;
  userName: string;
  isOpen: boolean;
  onClose: () => void;
}

export const UserTicketsModal = ({ userId, userName, isOpen, onClose }: UserTicketsModalProps) => {
  const userTickets = useQuery(api.tickets.getUserTickets, { userId });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-md p-6 md:p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto bg-white">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Tickets for <span className="font-normal">{userName}</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            aria-label="Close modal"
          >
            &times;
          </button>
        </div>

        {userTickets ? (
          <Table>
            <TableCaption>A list of tickets for {userName}.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>Ticket Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Room</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {userTickets.map((ticket) => (
                <TableRow key={ticket._id}>
                  <TableCell>{ticket.event?.title}</TableCell>
                  <TableCell>{ticket.ticketType}</TableCell>
                  <TableCell>{ticket.status}</TableCell>
                  <TableCell>{ticket.quantity}</TableCell>
                  <TableCell>{ticket.roomDetails ? `${ticket.roomDetails.hotelName} - Room ${ticket.roomDetails.roomNumber}` : 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p>Loading tickets...</p>
        )}
      </div>
    </div>
  );
};