import Sidebar from "./Sidebar";
import { TabType } from "../../types";
import { ReactNode, useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Search } from "lucide-react";

interface DashboardLayoutProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
  children: ReactNode;
  currentUser: any;
}

const DashboardLayout = ({
  activeTab,
  setActiveTab,
  isEventOrganizer,
  children,
  currentUser,
}: DashboardLayoutProps) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  let welcomeName = "User";
  let displayRank = "";

  if (currentUser?.rank) {
    const rankMatch = currentUser.rank.match(/\(([^)]+)\)/);
    if (rankMatch && rankMatch[1]) {
      displayRank = rankMatch[1];
    } else {
      displayRank = currentUser.rank;
    }
  }

  if (currentUser?.name) {
    welcomeName = displayRank ? `${displayRank} ${currentUser.name}` : currentUser.name;
  }

  const getTabDisplayName = (tab: string) => {
    const names: { [key: string]: string } = {
      browse: "Dashboard",
      my_events: "My Events",
      create_event: "Create Event",
      my_registrations: "My Registrations",
      my_hotels: "My Hotels",
      manage_users: "User Management",
      transactions: "Transactions",
      hotel_management: "Hotel Management",
      profile: "Profile"
    };
    return names[tab] || tab.replace("_", " ");
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>

        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>

      <Sidebar
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isEventOrganizer={isEventOrganizer}
      />

      <div className={`flex-1 flex flex-col overflow-hidden relative z-10 transition-all duration-1000 ${isLoaded ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
        {/* Modern Header */}
        <header className="backdrop-blur-xl bg-white/10 border-b border-white/20 p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {getTabDisplayName(activeTab)}
                  </h2>
                  <p className="text-white/60 text-sm">
                    {activeTab === "browse" ? "Discover amazing events" :
                      activeTab === "profile" ? "Manage your account" :
                        activeTab === "create_event" ? "Create something amazing" :
                          "Manage your content"}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search Bar */}
              <div className="relative hidden md:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 backdrop-blur-sm w-64"
                />
              </div>

              {/* Notifications */}
              <button className="relative p-2 bg-white/10 rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 backdrop-blur-sm">
                <Bell className="w-5 h-5 text-white" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              </button>

              {/* User Profile */}
              <div className="flex items-center space-x-3 bg-white/10 rounded-xl border border-white/20 p-3 backdrop-blur-sm">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-semibold text-sm">
                    {welcomeName.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="hidden md:block">
                  <p className="text-white font-medium text-sm">{welcomeName}</p>
                  <p className="text-white/60 text-xs">
                    {isEventOrganizer ? "Event Organizer" : "Member"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;