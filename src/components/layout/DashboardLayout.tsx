import Sidebar from "./Sidebar";
import { TabType } from "../../types";
import { ReactNode, useState } from "react";
import { Menu } from "lucide-react";

interface DashboardLayoutProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
  children: ReactNode;
  currentUser: any;
}

const DashboardLayout = ({
  activeTab,
  setActiveTab,
  isEventOrganizer,
  children,
  currentUser,
}: DashboardLayoutProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  let welcomeName = "User";
  let displayRank = "";

  if (currentUser?.rank) {
    const rankMatch = currentUser.rank.match(/\(([^)]+)\)/);
    if (rankMatch && rankMatch[1]) {
      displayRank = rankMatch[1];
    } else {
      displayRank = currentUser.rank;
    }
  }

  if (currentUser?.name) {
    welcomeName = displayRank ? `${displayRank} ${currentUser.name}` : currentUser.name;
  }
  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isEventOrganizer={isEventOrganizer}
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Header */}
        <header className="bg-white shadow-md p-4 flex justify-between items-center">
          <div className="flex items-center">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors mr-3"
            >
              <Menu className="w-6 h-6" />
            </button>
            <h2 className="text-xl lg:text-2xl font-semibold text-gray-800 capitalize">
              {activeTab.replace("_", " ")}
            </h2>
          </div>
          <div className="flex items-center">
            <span className="text-gray-600 text-sm lg:text-base hidden sm:block mr-4">
              Welcome, {welcomeName}
            </span>
            <span className="text-gray-600 text-sm lg:text-base sm:hidden mr-4">
              {welcomeName.split(' ').slice(-1)[0]} {/* Show only last name on very small screens */}
            </span>
            {/* Additional header items can go here */}
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-3 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;