import Sidebar from "./Sidebar";
import { TabType } from "../../types";
import { ReactNode } from "react";

interface DashboardLayoutProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
  children: ReactNode;
  currentUser: any;
}

const DashboardLayout = ({
  activeTab,
  setActiveTab,
  isEventOrganizer,
  children,
  currentUser,
}: DashboardLayoutProps) => {
  let welcomeName = "User";
  let displayRank = "";

  if (currentUser?.rank) {
    const rankMatch = currentUser.rank.match(/\(([^)]+)\)/);
    if (rankMatch && rankMatch[1]) {
      displayRank = rankMatch[1];
    } else {
      displayRank = currentUser.rank;
    }
  }

  if (currentUser?.name) {
    welcomeName = displayRank ? `${displayRank} ${currentUser.name}` : currentUser.name;
  }
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isEventOrganizer={isEventOrganizer}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white shadow-md p-4 flex justify-between items-center">
          <h2 className="text-2xl font-semibold text-gray-800 capitalize">
            {activeTab.replace("_", " ")}
          </h2>
          <div className="flex items-center">
            <span className="text-gray-600 mr-4">
              Welcome, {welcomeName}
            </span>
            {/* Additional header items can go here */}
          </div>
        </header>
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;