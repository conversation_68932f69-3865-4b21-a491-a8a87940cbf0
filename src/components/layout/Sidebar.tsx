import {
  LayoutDashboard,
  Ticket,
  Users,
  PlusCircle,
  List,
  Hotel,
  Building2,
  User,
  ArrowLeftRight,
  X,
  Sparkles,
} from "lucide-react";
import { SignOutButton } from "../SignOutButton";
import { TabType } from "../../types";

interface SidebarProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
  isOpen?: boolean;
  onClose?: () => void;
}

const Sidebar = ({ activeTab, setActiveTab, isEventOrganizer, isOpen = true, onClose }: SidebarProps) => {
  const handleTabClick = (tab: TabType) => {
    setActiveTab(tab);
    // Close mobile menu when tab is selected
    if (onClose) {
      onClose();
    }
  };
  const navItems: { label: string; icon: React.ElementType; tab: TabType; organizerOnly: boolean }[] = [
    {
      label: "Dashboard",
      icon: LayoutDashboard,
      tab: "browse",
      organizerOnly: false,
    },
    { label: "My Events", icon: List, tab: "my_events", organizerOnly: true },
    {
      label: "Create Event",
      icon: PlusCircle,
      tab: "create_event",
      organizerOnly: true,
    },
    {
      label: "My Registrations",
      icon: Ticket,
      tab: "my_registrations",
      organizerOnly: false,
    },
    {
      label: "My Accommodation",
      icon: Hotel,
      tab: "my_accommodations",
      organizerOnly: false,
    },
    {
      label: "Manage Users",
      icon: Users,
      tab: "manage_users",
      organizerOnly: true,
    },
    {
      label: "Transactions",
      icon: ArrowLeftRight,
      tab: "transactions",
      organizerOnly: true,
    },
    {
      label: "Manage Accommodation",
      icon: Building2,
      tab: "hotel_management",
      organizerOnly: true,
    },
    { label: "Profile", icon: User, tab: "profile", organizerOnly: false },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && onClose && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
        flex flex-col h-full w-64
        bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 shadow-2xl
        transform transition-transform duration-300 ease-in-out lg:transform-none
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="p-4 border-b border-gray-700/50 bg-gray-800/50 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="inline-flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-lg font-bold text-white">KSJI Events</h1>
            </div>
            {/* Mobile close button */}
            {onClose && (
              <button
                onClick={onClose}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-3 overflow-y-auto">
          <ul className="space-y-1">
            {navItems.map((item) =>
              (!item.organizerOnly || isEventOrganizer) ? (
                <li key={item.tab}>
                  <button
                    onClick={() => handleTabClick(item.tab)}
                    className={`w-full group flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 ${activeTab === item.tab
                      ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]"
                      : "text-gray-300 hover:text-white hover:bg-gray-700/50 hover:transform hover:scale-[1.01]"
                      }`}
                  >
                    <item.icon className={`mr-3 h-4 w-4 transition-colors ${activeTab === item.tab ? "text-blue-200" : "text-gray-400 group-hover:text-gray-200"
                      }`} />
                    <span className="truncate">{item.label}</span>
                  </button>
                </li>
              ) : null
            )}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-3 border-t border-gray-700/50 bg-gray-800/30">
          <SignOutButton />
        </div>
      </div>
    </>
  );
};

export default Sidebar;