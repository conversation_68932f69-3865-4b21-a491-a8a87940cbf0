import {
  LayoutDashboard,
  Calendar,
  Ticket,
  Users,
  PlusCircle,
  List,
  Hotel,
  Building2,
  User,
  LogOut,
  ArrowLeftRight,
} from "lucide-react";
import { SignOutButton } from "../SignOutButton";
import { TabType } from "../../types";

interface SidebarProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
}

const Sidebar = ({ activeTab, setActiveTab, isEventOrganizer }: SidebarProps) => {
  const navItems: { label: string; icon: React.ElementType; tab: TabType; organizerOnly: boolean }[] = [
    {
      label: "Dashboard",
      icon: LayoutDashboard,
      tab: "browse",
      organizerOnly: false,
    },
    { label: "My Events", icon: List, tab: "my_events", organizerOnly: true },
    {
      label: "Create Event",
      icon: PlusCircle,
      tab: "create_event",
      organizerOnly: true,
    },
    {
      label: "My Registrations",
      icon: Ticket,
      tab: "my_registrations",
      organizerOnly: false,
    },
    {
      label: "My Accommodation",
      icon: Hotel,
      tab: "my_accommodations",
      organizerOnly: false,
    },
    {
      label: "Manage Users",
      icon: Users,
      tab: "manage_users",
      organizerOnly: true,
    },
    {
      label: "Transactions",
      icon: ArrowLeftRight,
      tab: "transactions",
      organizerOnly: true,
    },
    {
      label: "Manage Accommodation",
      icon: Building2,
      tab: "hotel_management",
      organizerOnly: true,
    },
    { label: "Profile", icon: User, tab: "profile", organizerOnly: false },
  ];

  return (
    <div className="flex flex-col h-full bg-gray-800 text-white w-64">
      <div className="p-4 border-b border-gray-700">
        <h1 className="text-2xl font-bold">KSJI Events</h1>
      </div>
      <nav className="flex-1 p-2">
        <ul>
          {navItems.map((item) =>
            (!item.organizerOnly || isEventOrganizer) ? (
              <li key={item.tab}>
                <button
                  onClick={() => setActiveTab(item.tab)}
                  className={`w-full flex items-center p-2 rounded-md text-sm font-medium transition-colors ${activeTab === item.tab
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white"
                    }`}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.label}
                </button>
              </li>
            ) : null
          )}
        </ul>
      </nav>
      <div className="p-4 border-t border-gray-700">
        <SignOutButton />
      </div>
    </div>
  );
};

export default Sidebar;