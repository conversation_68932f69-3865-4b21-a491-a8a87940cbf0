import {
  LayoutDashboard,
  Calendar,
  Ticket,
  Users,
  PlusCircle,
  List,
  Hotel,
  Building2,
  User,
  LogOut,
  ArrowLeftRight,
  Sparkles,
  Crown,
} from "lucide-react";
import { SignOutButton } from "../SignOutButton";
import { TabType } from "../../types";
import { useState, useEffect } from "react";

interface SidebarProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
}

const Sidebar = ({ activeTab, setActiveTab, isEventOrganizer }: SidebarProps) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const navItems: {
    label: string;
    icon: React.ElementType;
    tab: TabType;
    organizerOnly: boolean;
    gradient?: string;
  }[] = [
      {
        label: "Dashboard",
        icon: LayoutDashboard,
        tab: "browse",
        organizerOnly: false,
        gradient: "from-blue-500 to-cyan-500"
      },
      {
        label: "My Events",
        icon: List,
        tab: "my_events",
        organizerOnly: true,
        gradient: "from-purple-500 to-pink-500"
      },
      {
        label: "Create Event",
        icon: PlusCircle,
        tab: "create_event",
        organizerOnly: true,
        gradient: "from-green-500 to-emerald-500"
      },
      {
        label: "My Registrations",
        icon: Ticket,
        tab: "my_registrations",
        organizerOnly: false,
        gradient: "from-orange-500 to-red-500"
      },
      {
        label: "My Hotels",
        icon: Hotel,
        tab: "my_hotels",
        organizerOnly: false,
        gradient: "from-indigo-500 to-purple-500"
      },
      {
        label: "Manage Users",
        icon: Users,
        tab: "manage_users",
        organizerOnly: true,
        gradient: "from-pink-500 to-rose-500"
      },
      {
        label: "Transactions",
        icon: ArrowLeftRight,
        tab: "transactions",
        organizerOnly: true,
        gradient: "from-yellow-500 to-orange-500"
      },
      {
        label: "Hotel Management",
        icon: Building2,
        tab: "hotel_management",
        organizerOnly: true,
        gradient: "from-teal-500 to-cyan-500"
      },
      {
        label: "Profile",
        icon: User,
        tab: "profile",
        organizerOnly: false,
        gradient: "from-violet-500 to-purple-500"
      },
    ];

  return (
    <div className={`flex flex-col h-full backdrop-blur-xl bg-black/20 border-r border-white/20 text-white w-72 relative transition-all duration-1000 ${isLoaded ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none"></div>

      {/* Header */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
              KSJI Events
            </h1>
            <p className="text-white/60 text-xs">Event Management Platform</p>
          </div>
        </div>

        {isEventOrganizer && (
          <div className="mt-4 flex items-center space-x-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-2">
            <Crown className="w-4 h-4 text-yellow-400" />
            <span className="text-yellow-300 text-xs font-medium">Event Organizer</span>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navItems.map((item, index) =>
          (!item.organizerOnly || isEventOrganizer) ? (
            <div
              key={item.tab}
              className={`transform transition-all duration-500 ${isLoaded ? 'translate-x-0 opacity-100' : 'translate-x-4 opacity-0'}`}
              style={{ transitionDelay: `${index * 50}ms` }}
            >
              <button
                onClick={() => setActiveTab(item.tab)}
                className={`w-full flex items-center p-3 rounded-xl text-sm font-medium transition-all duration-300 group relative overflow-hidden ${activeTab === item.tab
                    ? "bg-white/20 text-white shadow-lg border border-white/30"
                    : "text-white/70 hover:bg-white/10 hover:text-white border border-transparent"
                  }`}
              >
                {/* Active indicator */}
                {activeTab === item.tab && (
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 rounded-r-full"></div>
                )}

                {/* Icon with gradient background */}
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 bg-gradient-to-r ${item.gradient} shadow-lg`}>
                  <item.icon className="w-4 h-4 text-white" />
                </div>

                <span className="flex-1 text-left">{item.label}</span>

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
              </button>
            </div>
          ) : null
        )}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-white/10">
        <SignOutButton />
      </div>
    </div>
  );
};

export default Sidebar;