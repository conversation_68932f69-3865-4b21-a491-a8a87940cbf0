import {
  LayoutDashboard,
  Ticket,
  Users,
  PlusCircle,
  List,
  Hotel,
  Building2,
  User,
  ArrowLeftRight,
  Sparkles,
} from "lucide-react";
import { SignOutButton } from "../SignOutButton";
import { TabType } from "../../types";

interface SidebarProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  isEventOrganizer: boolean;
}

const Sidebar = ({ activeTab, setActiveTab, isEventOrganizer }: SidebarProps) => {
  const navItems: { label: string; icon: React.ElementType; tab: TabType; organizerOnly: boolean }[] = [
    {
      label: "Dashboard",
      icon: LayoutDashboard,
      tab: "browse",
      organizerOnly: false,
    },
    { label: "My Events", icon: List, tab: "my_events", organizerOnly: true },
    {
      label: "Create Event",
      icon: PlusCircle,
      tab: "create_event",
      organizerOnly: true,
    },
    {
      label: "My Registrations",
      icon: Ticket,
      tab: "my_registrations",
      organizerOnly: false,
    },
    {
      label: "My Accommodation",
      icon: Hotel,
      tab: "my_accommodations",
      organizerOnly: false,
    },
    {
      label: "Manage Users",
      icon: Users,
      tab: "manage_users",
      organizerOnly: true,
    },
    {
      label: "Transactions",
      icon: ArrowLeftRight,
      tab: "transactions",
      organizerOnly: true,
    },
    {
      label: "Manage Accommodation",
      icon: Building2,
      tab: "hotel_management",
      organizerOnly: true,
    },
    { label: "Profile", icon: User, tab: "profile", organizerOnly: false },
  ];

  return (
    <div className="flex flex-col h-full w-64 relative overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute top-20 left-20 w-40 h-40 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      {/* Glassmorphism overlay */}
      <div className="absolute inset-0 backdrop-blur-xl bg-white/5 border-r border-white/10"></div>

      {/* Content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-bold text-white">KSJI Events</h1>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navItems.map((item) =>
              (!item.organizerOnly || isEventOrganizer) ? (
                <li key={item.tab}>
                  <button
                    onClick={() => setActiveTab(item.tab)}
                    className={`w-full group flex items-center p-3 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-[1.02] ${activeTab === item.tab
                      ? "bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-white/20 shadow-lg backdrop-blur-sm"
                      : "text-white/70 hover:text-white hover:bg-white/10 backdrop-blur-sm border border-transparent hover:border-white/10"
                      }`}
                  >
                    <item.icon className={`mr-3 h-5 w-5 transition-colors ${activeTab === item.tab ? "text-purple-300" : "text-white/50 group-hover:text-white/80"
                      }`} />
                    {item.label}
                  </button>
                </li>
              ) : null
            )}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-white/10">
          <SignOutButton />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;