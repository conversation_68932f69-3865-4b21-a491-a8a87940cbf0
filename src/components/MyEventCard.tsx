import { Calendar, MapPin, Ticket, <PERSON><PERSON><PERSON>, Edit, Banknote, Users, Trash2 } from "lucide-react";
import { EventType } from "../types";
import { Hotel } from "lucide-react";

interface MyEventCardProps {
  event: EventType;
  onEdit: (event: EventType) => void;
  onViewInsights: (eventId: string) => void;
  onManageAccommodations: (eventId: string) => void;
  onManageAttendance?: (eventId: string) => void;
  onDelete?: (eventId: string) => void;
}

const MyEventCard = ({ event, onEdit, onViewInsights, onManageAccommodations, onManageAttendance, onDelete }: MyEventCardProps) => {
  const {
    title,
    description,
    startDate,
    location,
    price,
    totalTickets,
    soldTickets,
    imageUrl,
  } = event;

  const progress = (totalTickets || 0) > 0 ? ((soldTickets || 0) / (totalTickets || 1)) * 100 : 0;
  const revenue = (totalTickets || 0) > 0 ? price * (soldTickets || 0) : 0;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img
        src={imageUrl || "https://via.placeholder.com/400x200"}
        alt={title}
        className="w-full h-32 sm:h-40 lg:h-48 object-cover"
      />
      <div className="p-3 sm:p-4">
        <h3 className="text-base sm:text-lg font-semibold mb-2 line-clamp-2">{title}</h3>
        <p className="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4 line-clamp-2">{description}</p>

        {/* Date and Location */}
        <div className="space-y-2 mb-3 sm:mb-4">
          <div className="flex items-center text-xs sm:text-sm text-gray-500">
            <Calendar className="w-3 h-3 sm:w-4 sm:h-4 mr-2 flex-shrink-0" />
            <span className="truncate">{startDate ? new Date(startDate).toLocaleDateString() : 'Date TBD'}</span>
          </div>
          <div className="flex items-center text-xs sm:text-sm text-gray-500">
            <MapPin className="w-3 h-3 sm:w-4 sm:h-4 mr-2 flex-shrink-0" />
            <span className="truncate">{location}</span>
          </div>
        </div>

        {/* Stats - Mobile Stacked, Desktop Side by Side */}
        <div className="space-y-2 sm:space-y-0 sm:flex sm:items-center sm:justify-between mb-3 sm:mb-4">
          <div className="flex items-center">
            <Ticket className="w-3 h-3 sm:w-4 sm:h-4 mr-2 text-blue-500 flex-shrink-0" />
            <span className="text-xs sm:text-sm font-semibold">
              {soldTickets || 0} / {totalTickets || 0} sold
            </span>
          </div>
          <div className="flex items-center">
            <Banknote className="w-3 h-3 sm:w-4 sm:h-4 mr-2 text-green-500 flex-shrink-0" />
            <span className="text-xs sm:text-sm font-semibold">
              Revenue: GH₵{revenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 sm:h-2.5 mb-3 sm:mb-4">
          <div
            className="bg-blue-600 h-2 sm:h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        {/* Action Buttons - Responsive Layout */}
        <div className="space-y-2 sm:space-y-3">
          {/* Primary Actions Row */}
          <div className="flex gap-2">
            <button
              onClick={() => onEdit(event)}
              className="flex-1 flex items-center justify-center bg-yellow-500 text-white px-2 sm:px-3 py-1.5 sm:py-2 rounded-md hover:bg-yellow-600 transition-colors text-xs sm:text-sm font-medium"
            >
              <Edit className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
              <span className="hidden xs:inline">Edit</span>
            </button>
            {onDelete && (
              <button
                onClick={() => onDelete(event._id)}
                className="flex items-center justify-center bg-red-600 text-white px-2 sm:px-3 py-1.5 sm:py-2 rounded-md hover:bg-red-700 transition-colors text-xs sm:text-sm font-medium"
                title="Delete Event"
              >
                <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
            )}
          </div>

          {/* Secondary Actions Row - Mobile: Single Column, Desktop: Grid */}
          <div className="flex flex-col sm:grid sm:grid-cols-2 gap-2">
            <button
              onClick={() => onViewInsights(event._id)}
              className="flex items-center justify-center bg-gray-600 text-white px-2 py-1.5 rounded-md hover:bg-gray-700 transition-colors text-xs"
            >
              <BarChart className="w-3 h-3 mr-1" />
              Insights
            </button>
            <button
              onClick={() => onManageAccommodations(event._id)}
              className="flex items-center justify-center bg-purple-500 text-white px-2 py-1.5 rounded-md hover:bg-purple-600 transition-colors text-xs"
            >
              <Hotel className="w-3 h-3 mr-1" />
              Hotels
            </button>
            {onManageAttendance && (
              <button
                onClick={() => onManageAttendance(event._id)}
                className="flex items-center justify-center bg-green-600 text-white px-2 py-1.5 rounded-md hover:bg-green-700 transition-colors text-xs sm:col-span-2"
              >
                <Users className="w-3 h-3 mr-1" />
                Attendance
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyEventCard;