import { Calendar, MapPin, Ticket, <PERSON><PERSON><PERSON>, Edit, Banknote, Users, Trash2 } from "lucide-react";
import { EventType } from "../types";
import { Hotel } from "lucide-react";

interface MyEventCardProps {
  event: EventType;
  onEdit: (event: EventType) => void;
  onViewInsights: (eventId: string) => void;
  onManageAccommodations: (eventId: string) => void;
  onManageAttendance?: (eventId: string) => void;
  onDelete?: (eventId: string) => void;
}

const MyEventCard = ({ event, onEdit, onViewInsights, onManageAccommodations, onManageAttendance, onDelete }: MyEventCardProps) => {
  const {
    title,
    description,
    startDate,
    location,
    price,
    totalTickets,
    soldTickets,
    imageUrl,
  } = event;

  const progress = totalTickets > 0 ? (soldTickets / totalTickets) * 100 : 0;
  const revenue = price * soldTickets;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img
        src={imageUrl || "https://via.placeholder.com/400x200"}
        alt={title}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{startDate ? new Date(startDate).toLocaleDateString() : 'Date TBD'}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <MapPin className="w-4 h-4 mr-2" />
          <span>{location}</span>
        </div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Ticket className="w-4 h-4 mr-2 text-blue-500" />
            <span className="text-sm font-semibold">
              {soldTickets} / {totalTickets} sold
            </span>
          </div>
          <div className="flex items-center">
            <Banknote className="w-4 h-4 mr-2 text-green-500" />
            <span className="text-sm font-semibold">
              Revenue: GH₵{revenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-end gap-2 flex-wrap">
          <button
            onClick={() => onViewInsights(event._id)}
            className="flex items-center bg-gray-600 text-white px-3 py-1.5 rounded-md hover:bg-gray-700 transition-colors text-sm"
          >
            <BarChart className="w-4 h-4 mr-1" />
            Insights
          </button>
          <button
            onClick={() => onManageAccommodations(event._id)}
            className="flex items-center bg-purple-500 text-white px-3 py-1.5 rounded-md hover:bg-purple-600 transition-colors text-sm"
          >
            <Hotel className="w-4 h-4 mr-1" />
            Hotels
          </button>
          {onManageAttendance && (
            <button
              onClick={() => onManageAttendance(event._id)}
              className="flex items-center bg-green-600 text-white px-3 py-1.5 rounded-md hover:bg-green-700 transition-colors text-sm"
            >
              <Users className="w-4 h-4 mr-1" />
              Attendance
            </button>
          )}
          <button
            onClick={() => onEdit(event)}
            className="flex items-center bg-yellow-500 text-white px-3 py-1.5 rounded-md hover:bg-yellow-600 transition-colors text-sm"
          >
            <Edit className="w-4 h-4 mr-1" />
            Edit
          </button>
          {onDelete && (
            <button
              onClick={() => onDelete(event._id)}
              className="flex items-center bg-red-600 text-white px-3 py-1.5 rounded-md hover:bg-red-700 transition-colors text-sm"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Delete
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MyEventCard;