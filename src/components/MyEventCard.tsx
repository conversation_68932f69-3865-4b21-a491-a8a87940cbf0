import { Calendar, MapPin, Ticket, DollarSign, <PERSON><PERSON><PERSON>, Edit, Banknote } from "lucide-react";
import { EventType } from "../types";

import { Hotel } from "lucide-react";

interface MyEventCardProps {
  event: EventType;
  onEdit: (event: EventType) => void;
  onViewInsights: (eventId: string) => void;
  onManageHotels: (eventId: string) => void;
}

const MyEventCard = ({ event, onEdit, onViewInsights, onManageHotels }: MyEventCardProps) => {
  const {
    title,
    description,
    startDate,
    location,
    price,
    totalTickets,
    soldTickets,
    imageUrl,
  } = event;

  const availableTickets = totalTickets - soldTickets;
  const progress = (soldTickets / totalTickets) * 100;
  const revenue = price * soldTickets;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img
        src={imageUrl || "https://via.placeholder.com/400x200"}
        alt={title}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{new Date(startDate).toLocaleDateString()}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <MapPin className="w-4 h-4 mr-2" />
          <span>{location}</span>
        </div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Ticket className="w-4 h-4 mr-2 text-blue-500" />
            <span className="text-sm font-semibold">
              {soldTickets} / {totalTickets} sold
            </span>
          </div>
          <div className="flex items-center">
            <Banknote className="w-4 h-4 mr-2 text-green-500" />
            <span className="text-sm font-semibold">
              Revenue: GH₵{revenue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={() => onEdit(event)}
            className="flex items-center bg-yellow-500 text-white px-3 py-1.5 rounded-md hover:bg-yellow-600 transition-colors text-sm"
          >
            <Edit className="w-4 h-4 mr-1" />
            Edit
          </button>
          <button
            onClick={() => onViewInsights(event._id)}
            className="flex items-center bg-gray-600 text-white px-3 py-1.5 rounded-md hover:bg-gray-700 transition-colors text-sm"
          >
            <BarChart className="w-4 h-4 mr-1" />
            View Insights
          </button>
          <button
            onClick={() => onManageHotels(event._id)}
            className="flex items-center bg-purple-500 text-white px-3 py-1.5 rounded-md hover:bg-purple-600 transition-colors text-sm"
          >
            <Hotel className="w-4 h-4 mr-1" />
            Manage Hotels
          </button>
        </div>
      </div>
    </div>
  );
};

export default MyEventCard;