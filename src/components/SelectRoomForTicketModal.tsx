import { useState } from "react";
import { EventType } from "../types";
import { Room, RoomSelection } from "./RoomSelection"; // Assuming RoomSelection exports Room type
import { toast } from "sonner";

interface SelectRoomForTicketModalProps {
  event: EventType;
  onClose: () => void;
  onRoomSelectedAndProceed: (room: Room) => void;
}

export function SelectRoomForTicketModal({
  event,
  onClose,
  onRoomSelectedAndProceed,
}: SelectRoomForTicketModalProps) {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);

  const handleProceed = () => {
    if (selectedRoom) {
      onRoomSelectedAndProceed(selectedRoom);
    } else {
      toast.error("Please select a room to proceed.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-bold text-gray-900">
            Select a Room for {event.title}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            &times;
          </button>
        </div>

        <div className="space-y-4">
          <RoomSelection eventId={event._id as any} onSelectRoom={setSelectedRoom} />
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleProceed}
            disabled={!selectedRoom}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Proceed to Payment
          </button>
        </div>
      </div>
    </div>
  );
}
