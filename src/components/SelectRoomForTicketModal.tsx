import { useState, useMemo, useEffect } from "react";
import { EventType } from "../types";
import { Room, RoomSelection } from "./RoomSelection"; // Assuming RoomSelection exports Room type
import { toast } from "sonner";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface SelectRoomForTicketModalProps {
  event: EventType;
  onClose: () => void;
  onRoomSelectedAndProceed: (room: Room, extraDays: number) => void;
  attendeeType?: string;
}

export function SelectRoomForTicketModal({
  event,
  onClose,
  onRoomSelectedAndProceed,
  attendeeType,
}: SelectRoomForTicketModalProps) {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [selectedExtraDays, setSelectedExtraDays] = useState<number>(0);

  const handleRoomSelection = (room: Room | null, extraDays: number) => {
    setSelectedRoom(room);
    setSelectedExtraDays(extraDays);
  };

  const eventDurationInDays = useMemo(() => {
    if (!event.startDate || !event.endDate) return 1; // Default to 1 day if dates are not set
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 1;
  }, [event.startDate, event.endDate]);

  const extraDaysStartDate = event.endDate ? new Date(event.endDate).getTime() : 0;
  const extraDaysEndDate = extraDaysStartDate + selectedExtraDays * 24 * 60 * 60 * 1000;

  const roomOccupancy = useQuery(
    api.bookings.getRoomOccupancyForDateRange,
    selectedRoom && selectedExtraDays > 0
      ? { roomId: selectedRoom._id, startDate: extraDaysStartDate, endDate: extraDaysEndDate }
      : "skip"
  );

  const totalPrice = useMemo(() => {
    if (!selectedRoom) return 0;

    const basePrice = eventDurationInDays * selectedRoom.pricePerBedPerDay;
    let extraDaysCost = 0;

    if (selectedExtraDays > 0) {
      let isRoomFullyOccupiedDuringExtraDays = false;
      if (roomOccupancy) {
        for (let d = extraDaysStartDate; d < extraDaysEndDate; d += 24 * 60 * 60 * 1000) {
          if ((roomOccupancy[d] || 0) >= selectedRoom.capacity) {
            isRoomFullyOccupiedDuringExtraDays = true;
            break;
          }
        }
      }

      if (isRoomFullyOccupiedDuringExtraDays) {
        extraDaysCost = selectedExtraDays * selectedRoom.pricePerBedPerDay; // Charge per bed
      } else {
        extraDaysCost = selectedExtraDays * selectedRoom.pricePerBedPerDay * selectedRoom.capacity; // Charge for all beds
      }
    }

    return basePrice + extraDaysCost;
  }, [selectedRoom, eventDurationInDays, selectedExtraDays, roomOccupancy, extraDaysStartDate, extraDaysEndDate]);

  const handleProceed = () => {
    if (selectedRoom) {
      onRoomSelectedAndProceed({ ...(selectedRoom as any), totalPrice }, selectedExtraDays);
    } else {
      toast.error("Please select a room to proceed.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-bold text-gray-900">
            Select a Room for {event.title}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            &times;
          </button>
        </div>

        <div className="space-y-4">
          <RoomSelection eventId={event._id as any} onSelectRoom={handleRoomSelection} attendeeType={attendeeType} />

          {selectedRoom && (
            <div>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900">Booking Summary</h3>
                <div className="mt-2 text-sm text-blue-800 space-y-1">
                  <p>Event Duration: {eventDurationInDays} days</p>
                  <p>Extra Days: {selectedExtraDays}</p>
                  <p className="font-bold">Total Price: GHS {totalPrice.toFixed(2)}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleProceed}
            disabled={!selectedRoom}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Proceed to Payment
          </button>
        </div>
      </div>
    </div>
  );
}
