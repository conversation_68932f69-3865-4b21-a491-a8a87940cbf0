import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Hotel, Bed, ArrowRight, X } from "lucide-react";

interface RoomManagementModalProps {
  ticketId: string;
  onClose: () => void;
  onSuccess: () => void;
}

export function RoomManagementModal({ ticketId, onClose, onSuccess }: RoomManagementModalProps) {
  const [activeTab, setActiveTab] = useState<"bed" | "room">("bed");
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [selectedBedNumber, setSelectedBedNumber] = useState<number | null>(null);

  // Queries
  const currentBooking = useQuery(api.bookings.getRoomBookingForTicket, { ticketId });
  const availableBedsInCurrentRoom = useQuery(
    api.bookings.getAvailableBedsInRoom,
    currentBooking?.roomId ? { roomId: currentBooking.roomId } : "skip"
  );
  const availableRoomsInHotel = useQuery(
    api.bookings.getAvailableRoomsInHotel,
    currentBooking?.hotel?._id ? {
      hotelId: currentBooking.hotel._id,
      currentRoomId: currentBooking.roomId,
      priceRange: currentBooking.room ? {
        min: currentBooking.room.price * 0.9,
        max: currentBooking.room.price * 1.1
      } : undefined
    } : "skip"
  );
  const availableBedsInSelectedRoom = useQuery(
    api.bookings.getAvailableBedsInRoom,
    selectedRoomId ? { roomId: selectedRoomId } : "skip"
  );

  // Mutations
  const changeBedNumber = useMutation(api.bookings.changeBedNumber);
  const changeRoom = useMutation(api.bookings.changeRoom);

  const handleChangeBed = async (newBedNumber: number) => {
    if (!currentBooking || currentBooking.bookingType !== "detailed") {
      toast.error("No detailed booking found to modify.");
      return;
    }

    try {
      await changeBedNumber({
        bookingId: currentBooking._id,
        newBedNumber
      });
      toast.success(`Successfully changed to bed ${newBedNumber}`);
      onSuccess();
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to change bed");
    }
  };

  const handleChangeRoom = async () => {
    if (!currentBooking || currentBooking.bookingType !== "detailed") {
      toast.error("No detailed booking found to modify.");
      return;
    }

    if (!selectedRoomId || !selectedBedNumber) {
      toast.error("Please select a room and bed number.");
      return;
    }

    try {
      await changeRoom({
        bookingId: currentBooking._id,
        newRoomId: selectedRoomId,
        newBedNumber: selectedBedNumber
      });
      toast.success("Successfully changed room and bed");
      onSuccess();
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to change room");
    }
  };

  if (!currentBooking) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-md w-full p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Room Management</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>
          <p className="text-gray-600">No room booking found for this ticket.</p>
          <button
            onClick={onClose}
            className="mt-4 w-full bg-gray-500 text-white py-2 rounded-md hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  if (currentBooking.bookingType !== "detailed") {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-md w-full p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Room Management</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>
          <div className="text-center py-4">
            <Hotel className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 mb-2">
              You have a room assigned but no specific bed allocation.
            </p>
            <p className="text-sm text-gray-500">
              Room: {currentBooking.room?.roomNumber} at {currentBooking.hotel?.name}
            </p>
            <p className="text-xs text-gray-400 mt-2">
              Contact event organizers for bed assignment changes.
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-full bg-gray-500 text-white py-2 rounded-md hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">Manage Room Booking</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Current Booking Info */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h3 className="font-semibold text-blue-900 mb-2">Current Booking</h3>
            <div className="text-sm text-blue-800">
              <p><strong>Hotel:</strong> {currentBooking.hotel?.name}</p>
              <p><strong>Room:</strong> {currentBooking.room?.roomNumber}</p>
              <p><strong>Bed:</strong> {currentBooking.bedNumber}</p>
              <p><strong>Event:</strong> {currentBooking.event?.title}</p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex border-b mb-6">
            <button
              onClick={() => setActiveTab("bed")}
              className={`px-4 py-2 font-medium ${
                activeTab === "bed"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Bed className="w-4 h-4 inline mr-2" />
              Change Bed
            </button>
            <button
              onClick={() => setActiveTab("room")}
              className={`px-4 py-2 font-medium ${
                activeTab === "room"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Hotel className="w-4 h-4 inline mr-2" />
              Change Room
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === "bed" && (
            <div>
              <h3 className="font-semibold mb-4">Available Beds in Current Room</h3>
              {availableBedsInCurrentRoom ? (
                <div className="grid grid-cols-4 gap-2 mb-4">
                  {Array.from({ length: availableBedsInCurrentRoom.totalCapacity }, (_, i) => {
                    const bedNumber = i + 1;
                    const isOccupied = availableBedsInCurrentRoom.occupiedBeds.includes(bedNumber);
                    const isCurrent = bedNumber === currentBooking.bedNumber;
                    const isAvailable = availableBedsInCurrentRoom.availableBeds.includes(bedNumber);

                    return (
                      <button
                        key={bedNumber}
                        onClick={() => !isCurrent && isAvailable ? handleChangeBed(bedNumber) : undefined}
                        disabled={isOccupied || isCurrent}
                        className={`p-3 rounded-lg border text-sm font-medium ${
                          isCurrent
                            ? "bg-blue-100 border-blue-300 text-blue-700"
                            : isAvailable
                            ? "bg-green-50 border-green-300 text-green-700 hover:bg-green-100"
                            : "bg-red-50 border-red-300 text-red-700 cursor-not-allowed"
                        }`}
                      >
                        Bed {bedNumber}
                        {isCurrent && <div className="text-xs">Current</div>}
                        {isOccupied && !isCurrent && <div className="text-xs">Occupied</div>}
                      </button>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">Loading bed availability...</div>
              )}
            </div>
          )}

          {activeTab === "room" && (
            <div>
              <h3 className="font-semibold mb-4">Available Rooms (Similar Price Range)</h3>
              {availableRoomsInHotel ? (
                <div className="space-y-3 mb-6">
                  {availableRoomsInHotel.map((room) => (
                    <div
                      key={room._id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        room.isCurrentRoom
                          ? "bg-blue-50 border-blue-300"
                          : selectedRoomId === room._id
                          ? "bg-green-50 border-green-300"
                          : "bg-gray-50 border-gray-300 hover:bg-gray-100"
                      }`}
                      onClick={() => !room.isCurrentRoom ? setSelectedRoomId(room._id) : undefined}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">Room {room.roomNumber}</h4>
                          <p className="text-sm text-gray-600">
                            Capacity: {room.capacity} | Price: GH₵{room.price}
                          </p>
                          <p className="text-sm text-gray-500">
                            Available beds: {room.availableBeds.length}
                          </p>
                        </div>
                        {room.isCurrentRoom && (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                            Current
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">Loading available rooms...</div>
              )}

              {/* Bed Selection for Selected Room */}
              {selectedRoomId && availableBedsInSelectedRoom && (
                <div>
                  <h4 className="font-medium mb-3">Select Bed in New Room</h4>
                  <div className="grid grid-cols-4 gap-2 mb-4">
                    {availableBedsInSelectedRoom.availableBeds.map((bedNumber) => (
                      <button
                        key={bedNumber}
                        onClick={() => setSelectedBedNumber(bedNumber)}
                        className={`p-3 rounded-lg border text-sm font-medium ${
                          selectedBedNumber === bedNumber
                            ? "bg-green-100 border-green-300 text-green-700"
                            : "bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100"
                        }`}
                      >
                        Bed {bedNumber}
                      </button>
                    ))}
                  </div>
                  
                  <button
                    onClick={handleChangeRoom}
                    disabled={!selectedBedNumber}
                    className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <ArrowRight className="w-4 h-4 mr-2" />
                    Confirm Room Change
                  </button>
                </div>
              )}
            </div>
          )}

          <div className="mt-6 pt-4 border-t">
            <button
              onClick={onClose}
              className="w-full bg-gray-500 text-white py-2 rounded-md hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
