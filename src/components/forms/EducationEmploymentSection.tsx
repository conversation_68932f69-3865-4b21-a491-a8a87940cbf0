import React from 'react';
import { FormField } from './FormField';
import { formOptions } from '../../types/form';

interface EducationEmploymentSectionProps {
  formData: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  onOtherFieldChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}

export const EducationEmploymentSection: React.FC<EducationEmploymentSectionProps> = ({
  formData,
  onChange,
  onOtherFieldChange,
  errors = {},
  disabled = false,
}) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
        Education & Employment
      </h3>
      
      <div className="grid grid-cols-1 gap-y-4 gap-x-6 sm:grid-cols-6">
        <div className="sm:col-span-3">
          <FormField
            label="Educational Background"
            name="educationalBackground"
            type="select"
            value={formData.educationalBackground || ''}
            onChange={onChange}
            options={formOptions.educationalBackgrounds}
            showOtherField={(val) => val === 'Other'}
            otherFieldName="otherEducationalBackgroundDetails"
            otherFieldValue={formData.otherEducationalBackgroundDetails}
            onOtherFieldChange={onOtherFieldChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Employment Status"
            name="employmentStatus"
            type="select"
            value={formData.employmentStatus || ''}
            onChange={onChange}
            options={formOptions.employmentStatuses}
            showOtherField={(val) => val === 'Other'}
            otherFieldName="otherEmploymentStatusDetails"
            otherFieldValue={formData.otherEmploymentStatusDetails}
            onOtherFieldChange={onOtherFieldChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Career/Profession"
            name="careerProfession"
            type="text"
            value={formData.careerProfession || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Current Place of Work"
            name="currentPlaceOfWork"
            type="text"
            value={formData.currentPlaceOfWork || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-6">
          <FormField
            label="Other Skills & Expertise"
            name="otherSkillsExpertise"
            type="textarea"
            value={formData.otherSkillsExpertise || ''}
            onChange={onChange}
            rows={3}
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default EducationEmploymentSection;
