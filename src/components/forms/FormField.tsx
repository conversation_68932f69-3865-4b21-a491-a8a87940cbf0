import React from 'react';
import { formOptions } from '../../types/form';

type InputType = 'text' | 'email' | 'password' | 'number' | 'date' | 'textarea' | 'select' | 'checkbox';

interface FormFieldProps {
  label: string;
  name: string;
  type: InputType;
  value: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  options?: string[];
  optionLabels?: string[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  rows?: number;
  showOtherField?: (value: any) => boolean;
  otherFieldName?: string;
  otherFieldValue?: string;
  onOtherFieldChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type,
  value,
  onChange,
  options = [],
  optionLabels = [],
  placeholder,
  required = false,
  disabled = false,
  className = '',
  rows = 3,
  showOtherField,
  otherFieldName,
  otherFieldValue,
  onOtherFieldChange
}) => {
  const renderInput = () => {
    switch (type) {
      case 'select':
        return (
          <select
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            disabled={disabled}
            className={`mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
          >
            <option value="">Select {label}</option>
            {options.map((option, index) => (
              <option key={option} value={option}>
                {optionLabels[index] || option}
              </option>
            ))}
          </select>
        );
      case 'textarea':
        return (
          <textarea
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            rows={rows}
            disabled={disabled}
            className={`mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
            placeholder={placeholder}
          />
        );
      default:
        return (
          <input
            type={type}
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            disabled={disabled}
            className={`mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
            placeholder={placeholder}
          />
        );
    }
  };

  return (
    <div className="mb-4">
      <label htmlFor={name} className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500">*</span>}
      </label>
      <div className="mt-1">
        {renderInput()}
        {showOtherField?.(value) && otherFieldName && onOtherFieldChange && (
          <div className="mt-2">
            <input
              type="text"
              name={otherFieldName}
              value={otherFieldValue || ''}
              onChange={onOtherFieldChange}
              className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              placeholder={`Specify ${label.toLowerCase()}`}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default FormField;
