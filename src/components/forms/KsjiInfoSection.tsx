import React from 'react';
import { FormField } from './FormField';
import { Id } from '../../../convex/_generated/dataModel';

interface KsjiInfoSectionProps {
  formData: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
  grands?: { _id: Id<"grands">; name: string }[];
  districts?: { _id: Id<"districts">; name: string }[];
  commanderies?: { _id: Id<"commanderiesAuxiliaries">; name: string }[];
  offices?: { _id: Id<"ksjiOffices">; name: string }[];
}

export const KsjiInfoSection: React.FC<KsjiInfoSectionProps> = ({
  formData,
  onChange,
  errors = {},
  disabled = false,
  grands = [],
  districts = [],
  commanderies = [],
  offices = []
}) => {
  const rankOptions = [
    "1st Lieutenant (1Lt.)",
    "2nd Lieutenant (2Lt)",
    "Captain (Capt.)",
    "Major (Maj.)",
    "Lieutenant Colonel (Lt. Col)",
    "Colonel (Col)",
    "Brigadier General (BGen.)",
    "Major General (MGen.)",
    "Lieutenant General (Lt. Gen.)",
    "General",
    "Noble Sister (NS)"
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
        KSJI Information
      </h3>
      
      <div className="grid grid-cols-1 gap-y-4 gap-x-6 sm:grid-cols-6">
        <div className="sm:col-span-3">
          <FormField
            label="Member Number"
            name="memberNumber"
            type="text"
            value={formData.memberNumber || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Year of Initiation"
            name="yearOfInitiation"
            type="number"
            value={formData.yearOfInitiation || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Rank"
            name="rank"
            type="select"
            value={formData.rank || ''}
            onChange={onChange}
            options={rankOptions}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Current Office"
            name="currentOffice"
            type="select"
            value={formData.currentOffice || ''}
            onChange={onChange}
            options={offices.map(office => office.name)}
            disabled={disabled}
          />
        </div>
        
        {/* Grand Selection */}
        <div className="sm:col-span-2">
          <FormField
            label="Grand"
            name="grandId"
            type="select"
            value={formData.grandId || ''}
            onChange={onChange}
            options={['', ...grands.map(grand => grand._id)]}
            optionLabels={['Select Grand', ...grands.map(grand => grand.name)]}
            disabled={disabled}
          />
        </div>
        
        {/* District Selection - Only show if a grand is selected */}
        {formData.grandId && (
          <div className="sm:col-span-2">
            <FormField
              label="District"
              name="districtId"
              type="select"
              value={formData.districtId || ''}
              onChange={onChange}
              options={['', ...districts.map(district => district._id)]}
              optionLabels={['Select District', ...districts.map(district => district.name)]}
              disabled={disabled}
            />
          </div>
        )}
        
        {/* Commandery Selection - Only show if a district is selected */}
        {formData.districtId && (
          <div className="sm:col-span-2">
            <FormField
              label="Commandery/Auxiliary"
              name="commanderyAuxiliaryId"
              type="select"
              value={formData.commanderyAuxiliaryId || ''}
              onChange={onChange}
              options={['', ...commanderies.map(c => c._id)]}
              optionLabels={['Select Commandery/Auxiliary', ...commanderies.map(c => c.name)]}
              disabled={disabled}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default KsjiInfoSection;
