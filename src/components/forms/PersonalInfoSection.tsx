import React from 'react';
import { FormField } from './FormField';
import { formOptions } from '../../types/form';

interface PersonalInfoSectionProps {
  formData: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  onOtherFieldChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}

export const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  formData,
  onChange,
  onOtherFieldChange,
  errors = {},
  disabled = false,
}) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
        Personal Information
      </h3>
      
      <div className="grid grid-cols-1 gap-y-4 gap-x-6 sm:grid-cols-6">
        <div className="sm:col-span-2">
          <FormField
            label="Title"
            name="title"
            type="select"
            value={formData.title || ''}
            onChange={onChange}
            options={formOptions.titles}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-4">
          <FormField
            label="Full Name"
            name="name"
            type="text"
            value={formData.name || ''}
            onChange={onChange}
            required
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Gender"
            name="gender"
            type="select"
            value={formData.gender || ''}
            onChange={onChange}
            options={formOptions.genders}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Date of Birth"
            name="dateOfBirth"
            type="date"
            value={formData.dateOfBirth || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Marital Status"
            name="maritalStatus"
            type="select"
            value={formData.maritalStatus || ''}
            onChange={onChange}
            options={formOptions.maritalStatuses}
            showOtherField={(val) => val === 'Other'}
            otherFieldName="otherMaritalStatusDetails"
            otherFieldValue={formData.otherMaritalStatusDetails}
            onOtherFieldChange={onOtherFieldChange}
            disabled={disabled}
          />
        </div>
        
        <div className="sm:col-span-3">
          <FormField
            label="Phone Number"
            name="phoneNumber"
            type="tel"
            value={formData.phoneNumber || ''}
            onChange={onChange}
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
