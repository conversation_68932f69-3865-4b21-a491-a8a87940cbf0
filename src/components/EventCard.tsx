import { Calendar, MapPin, Users, Clock, CheckCircle, Ticket } from "lucide-react";
import { EventType } from "../types";
import { format } from 'date-fns';

interface EventCardProps {
  event: EventType;
  onSelect: (event: EventType) => void;
  isUserRegistered?: boolean;
  compact?: boolean;
}

const EventCard = ({ event, onSelect, isUserRegistered, compact = false }: EventCardProps) => {
  const {
    title,
    description,
    startDate,
    location,
    price,
    totalTickets,
    soldTickets,
    imageUrl,
  } = event;

  const availableTickets = totalTickets - soldTickets;
  const progress = Math.round((soldTickets / totalTickets) * 100);
  const eventDate = new Date(startDate);
  const timeString = format(eventDate, 'h:mm a');
  const dateString = format(eventDate, 'MMM d, yyyy');

  if (!compact) {
    return (
      <div
        className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
        onClick={() => onSelect(event)}
      >
        <div className="grid grid-cols-12 gap-x-6 p-4 items-center">
          {/* Image, Title, Description */}
          <div className="col-span-12 md:col-span-5 flex items-start gap-4">
            <img
              src={imageUrl || "https://via.placeholder.com/112x112?text=Event"}
              alt={title}
              className="w-28 h-28 object-cover rounded-lg flex-shrink-0"
            />
            <div className="flex flex-col justify-center h-28">
              <div className="flex items-center gap-2">
                <span className="text-xs font-semibold text-purple-700 bg-purple-100 px-2.5 py-1 rounded-full self-start">
                  {event.category || 'General'}
                </span>
                {isUserRegistered && (
                  <span className="flex items-center text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Registered
                  </span>
                )}
              </div>
              <h3 className="text-base font-bold text-gray-900 mt-2 line-clamp-1">{title}</h3>
              <p className="text-sm text-gray-500 mt-1 line-clamp-2">{description}</p>
            </div>
          </div>

          {/* Location & Date */}
          <div className="col-span-12 md:col-span-3 flex flex-col justify-center gap-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
              <span className="truncate">{location}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
              <span>{dateString} - {event.endDate ? format(new Date(event.endDate), 'MMM d, yyyy') : 'Date TBD'} • {timeString}</span>
            </div>
          </div>

          {/* Progress */}
          <div className="col-span-12 md:col-span-2">
            <div className="text-center">
              <span className="text-lg font-bold text-gray-900">{progress}%</span>
              <div className="w-full bg-gray-200 rounded-full h-2 my-1">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: `${progress}%` }} />
              </div>
              <span className="text-xs text-gray-500">Ticket Sold</span>
            </div>
          </div>

          {/* Tickets Left */}
          <div className="col-span-12 md:col-span-1 text-center">
            <div className="flex items-center justify-center gap-1">
              <Ticket className="w-5 h-5 text-gray-400" />
              <span className="text-lg font-bold text-gray-900">{availableTickets}</span>
            </div>
            <div className="text-xs text-gray-500 mt-1">Tickets Left</div>
          </div>

          {/* Price */}
          <div className="col-span-12 md:col-span-1 flex items-center justify-end">
            <div className="text-lg font-bold text-purple-600 bg-purple-100 rounded-lg px-4 py-2">
              {price === 0 ? 'Free' : `GH₵${price}`}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Original card layout (kept for backwards compatibility)
  return (
    <div
      className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform transform hover:scale-105"
      onClick={() => onSelect(event)}
    >
      <img
        src={imageUrl || "https://via.placeholder.com/400x200"}
        alt={title}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold">{title}</h3>
          {isUserRegistered && (
            <div className="flex items-center text-green-600 text-xs bg-green-50 px-2 py-1 rounded-full">
              <CheckCircle className="w-3 h-3 mr-1" />
              Registered
            </div>
          )}
        </div>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{new Date(startDate).toLocaleDateString()} - {event.endDate ? new Date(event.endDate).toLocaleDateString() : 'Date TBD'}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <MapPin className="w-4 h-4 mr-2" />
          <span>{location}</span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Ticket className="w-4 h-4 mr-2 text-blue-500" />
            <span className="text-sm font-semibold">
              {availableTickets} tickets left
            </span>
          </div>
          <div className="flex items-center">
            <span className="text-sm font-semibold">
              {price === 0 ? "Free" : `GH₵${price}`}
            </span>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-4">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default EventCard;