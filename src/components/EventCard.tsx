import { Calendar, MapPin, Ticket, DollarSign, Banknote, CheckCircle } from "lucide-react";
import { EventType } from "../types";

interface EventCardProps {
  event: EventType;
  onSelect: (event: EventType) => void;
  isUserRegistered?: boolean;
}

const EventCard = ({ event, onSelect, isUserRegistered }: EventCardProps) => {
  const {
    title,
    description,
    startDate,
    location,
    price,
    totalTickets,
    soldTickets,
    imageUrl,
  } = event;

  const availableTickets = totalTickets - soldTickets;
  const progress = (soldTickets / totalTickets) * 100;

  return (
    <div
      className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform transform hover:scale-105"
      onClick={() => onSelect(event)}
    >
      <img
        src={imageUrl || "https://via.placeholder.com/400x200"}
        alt={title}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold">{title}</h3>
          {isUserRegistered && (
            <div className="flex items-center text-green-600 text-xs bg-green-50 px-2 py-1 rounded-full">
              <CheckCircle className="w-3 h-3 mr-1" />
              Registered
            </div>
          )}
        </div>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{new Date(startDate).toLocaleDateString()}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <MapPin className="w-4 h-4 mr-2" />
          <span>{location}</span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Ticket className="w-4 h-4 mr-2 text-blue-500" />
            <span className="text-sm font-semibold">
              {availableTickets} tickets left
            </span>
          </div>
          <div className="flex items-center">
            <Banknote className="w-4 h-4 mr-2 text-green-500" />
            <span className="text-sm font-semibold">
              {price === 0 ? "Free" : `GH₵ ${price}`}
            </span>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-4">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default EventCard;