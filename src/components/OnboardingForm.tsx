import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";


// Define a type for the form data for better type safety
interface OnboardingFormData {
  name?: string;
  title?: string;
  gender?: string;
  dateOfBirth?: string; // HTML date input gives string
  maritalStatus?: string;
  otherMaritalStatusDetails?: string;
  grandId?: Id<"grands">;
  districtId?: Id<"districts">;
  commanderyAuxiliaryId?: Id<"commanderiesAuxiliaries">;
  rank?: string;
  currentOffice?: string;
  yearOfInitiation?: string; // Will be string from input, convert to number on submit
  phoneNumber?: string;
  educationalBackground?: string;
  otherEducationalBackgroundDetails?: string;
  employmentStatus?: string;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
}
export function OnboardingForm({ onSuccess }: { onSuccess: () => void }) {
  const [formData, setFormData] = useState<OnboardingFormData>({});
  const updateUserProfile = useMutation(api.users.updateUserProfile);

  const grands = useQuery(api.data.listGrands);
  const districts = useQuery(api.data.listDistrictsByGrand, formData.grandId ? { grandId: formData.grandId } : "skip");
  const commanderies = useQuery(api.data.listCommanderiesByDistrict, formData.districtId ? { districtId: formData.districtId } : "skip");
  const offices = useQuery(api.data.listOffices);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prevFormData => ({ ...prevFormData, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Create a mutable copy of formData to process before sending
      const processedUpdates: Record<string, any> = { ...formData };

      // Handle yearOfInitiation: convert to number or handle invalid/empty input
      const yearInput = formData.yearOfInitiation;
      if (yearInput !== undefined && yearInput !== null) {
        if (yearInput.trim() === "") {
          // If the input is an empty string, treat as "not set" for an optional field.
          // Delete the key so it's not sent, or send null if your backend expects that.
          delete processedUpdates.yearOfInitiation;
        } else {
          const yearAsNumber = parseFloat(yearInput);
          if (!isNaN(yearAsNumber)) {
            processedUpdates.yearOfInitiation = yearAsNumber;
          } else {
            // If it's a non-empty string but not a valid number
            toast.error("Invalid Year of Initiation. Please enter a valid number.");
            return; // Stop submission
          }
        }
      }
      // If yearInput was initially undefined or null, it remains as is (or absent) in processedUpdates.

      await updateUserProfile({ updates: processedUpdates });
      toast.success("Profile updated successfully!");
      onSuccess();
    } catch (error) {
      console.error("Failed to update profile:", error);
      if (error instanceof Error && (error as any).data) {
        console.error("Convex error data:", (error as any).data);
      }
      toast.error("Failed to update profile. Check console for more details.");
    }
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full p-6 max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold mb-4">Welcome!</h2>
        <p className="text-gray-600 mb-6">Please complete your profile to continue.</p>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
                Personal Information
              </h3>
              <input type="text" name="name" placeholder="Full Name" onChange={handleChange} className="w-full p-2 border rounded-md" required />
              <select name="title" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Title</option>
                {["Mr.", "Mrs.", "Ms", "Dr.", "Prof.", "Ing", "Hon", "Rev.", "Ps.", "Aps.", "ESQ", "Suv.", "Chief", "Nana"].map(t => <option key={t} value={t}>{t}</option>)}
              </select>
              <select name="gender" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
              </select>
              <input type="date" name="dateOfBirth" onChange={handleChange} className="w-full p-2 border rounded-md" />
              <select name="maritalStatus" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Marital Status</option>
                {["Single", "Married", "Divorced", "Widowed", "Other"].map(s => <option key={s} value={s}>{s}</option>)}
              </select>
              {formData.maritalStatus === "Other" && <input type="text" name="otherMaritalStatusDetails" placeholder="Other Marital Status Details" onChange={handleChange} className="w-full p-2 border rounded-md" />}
            </div>

            {/* Order Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
                Order Information
              </h3>
              <select name="grandId" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Grand</option>
                {grands?.map(g => <option key={g._id} value={g._id}>{g.name}</option>)}
              </select>
              <select name="districtId" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select District</option>
                {districts?.map(d => <option key={d._id} value={d._id}>{d.name}</option>)}
              </select>
              <select name="commanderyAuxiliaryId" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Commandery/Auxiliary</option>
                {commanderies?.map(c => <option key={c._id} value={c._id}>{c.name}</option>)}
              </select>
              <select name="rank" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Rank</option>
                {rankOptions.map(r => <option key={r.value} value={r.value}>{r.label}</option>)}
              </select>
              <select name="currentOffice" onChange={handleChange} className="w-full p-2 border rounded-md">
                <option value="">Select Current Office</option>
                {offices?.map(o => <option key={o._id} value={o.name}>{o.name}</option>)}
              </select>
              <input type="number" name="yearOfInitiation" placeholder="Year of Initiation" onChange={handleChange} className="w-full p-2 border rounded-md" />
              <input type="tel" name="phoneNumber" placeholder="Phone Number" onChange={handleChange} className="w-full p-2 border rounded-md" />
            </div>
          </div>

          {/* Education & Employment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2 mt-6">
              Education & Employment
            </h3>
            <select name="educationalBackground" onChange={handleChange} className="w-full p-2 border rounded-md">
              <option value="">Select Educational Background</option>
              {educationalBackgroundOptions.map(bg => (
                <option key={bg} value={bg}>{bg}</option>
              ))}
            </select>
            {formData.educationalBackground === "Other" && (
              <input
                type="text"
                name="otherEducationalBackgroundDetails"
                placeholder="Please specify your educational background"
                onChange={handleChange}
                className="w-full p-2 border rounded-md"
              />
            )}
            <select name="employmentStatus" onChange={handleChange} className="w-full p-2 border rounded-md">
              <option value="">Select Employment Status</option>
              {["Employed", "Self-Employed", "Unemployed", "Student", "Other"].map(s => <option key={s} value={s}>{s}</option>)}
            </select>
            {formData.employmentStatus === "Other" && <input type="text" name="otherEmploymentStatusDetails" placeholder="Other Employment Status Details" onChange={handleChange} className="w-full p-2 border rounded-md" />}
            <input type="text" name="careerProfession" placeholder="Career/Profession" onChange={handleChange} className="w-full p-2 border rounded-md" />
            <input type="text" name="currentPlaceOfWork" placeholder="Current Place of Work" onChange={handleChange} className="w-full p-2 border rounded-md" />
          </div>

          {/* Skills & Expertise */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2 mt-6">
              Skills & Expertise
            </h3>
            <textarea name="otherSkillsExpertise" placeholder="Other Skills/Expertise" onChange={handleChange} className="w-full p-2 border rounded-md" />
          </div>

          <button
            type="submit"
            className="w-full bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
          >
            Save and Continue
          </button>
        </form>
      </div>
    </div>
  );
}

// Educational background options (matching convex/schema.ts)
const educationalBackgroundOptions = [
  "Primary",
  "Secondary",
  "Tertiary",
  "Vocational",
  "Post-Tertiary",
  "Other"
] as const;

const rankOptions = [
  { value: "1st Lieutenant (1Lt.)", label: "1st Lieutenant (1Lt.)" },
  { value: "Noble Sister (NS)", label: "Noble Sister (NS)" },
  { value: "2nd Lieutenant (2Lt)", label: "2nd Lieutenant (2Lt)" },
  { value: "Captain (Capt.)", label: "Captain (Capt.)" },
  { value: "Major (Maj.)", label: "Major (Maj.)" },
  { value: "Lieutenant Colonel (Lt. Col)", label: "Lieutenant Colonel (Lt. Col)" },
  { value: "Colonel (Col)", label: "Colonel (Col)" },
  { value: "Brigadier General (BGen.)", label: "Brigadier General (BGen.)" },
  { value: "Major General (MGen.)", label: "Major General (MGen.)" },
  { value: "Lieutenant General (Lt. Gen.)", label: "Lieutenant General (Lt. Gen.)" },
  { value: "General", label: "General" },
] as const;