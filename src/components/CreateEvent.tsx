import { useState, useCallback } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { FileWithPath } from 'react-dropzone';
import { ImageDropzone } from "./ImageDropzone";
import { CsvDropzone } from "./CsvDropzone";
import { EventLevel } from "@/types"; // Ensure EventLevel is imported if used for typing
import { EventFormFields } from "./EventFormFields";

// Enums from your specifications
const eventLevels = ["National", "Grand", "District", "Local"];
const allowedAttendeeTypes = ["VIP", "Delegates", "Observers", "Guests"]; // Match spec: VIP, delegates, observers, guests
const categories = ["National", "District", "Grand", "Local", "All"]; // Or your defined categories

export function CreateEvent() {
  const [formData, setFormData] = useState({
    title: "", // Standardized to title
    description: "",
    startDate: "", // Will be datetime-local
    endDate: "",   // Will be datetime-local
    startTime: "", // Will be time
    endTime: "",   // Will be time
    price: 0,      // number (price or free)
    level: eventLevels[0], // Default to first level
    attendees: [] as string[],
    location: "", // textarea
    imageUrl: "", // textinput
    registrationOpenDate: "", // date
    registrationCloseDate: "", // date
    category: categories[0], // Default to first category
    totalTickets: 0, // Initialize as number
  });
  const [exemptedEmailsFile, setExemptedEmailsFile] = useState<FileWithPath | null>(null);
  const [parsedExemptedEmails, setParsedExemptedEmails] = useState<string[]>([]);
  const [featuredImageFile, setFeaturedImageFile] = useState<FileWithPath | null>(null);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const currentUser = useQuery(api.users.getCurrentUserWithOrgData);

  // Callbacks for dropzone components
  const handleFeaturedImageDrop = useCallback((file: FileWithPath | null) => {
    setFeaturedImageFile(file);
  }, []);

  const handleExemptedEmailsDrop = useCallback((file: FileWithPath | null) => {
    setExemptedEmailsFile(file);
  }, []);

  const handleExemptedEmailsParsed = useCallback((emails: string[]) => {
    setParsedExemptedEmails(emails);
  }, []);

  // Helper function to parse CSV (assuming one column of emails, no header)
  const parseEmailsCSV = async (file: File): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const text = event.target?.result as string;
          const lines = text.split(/\r\n|\n/).filter(line => line.trim() !== '');
          const emails = lines.map(line => {
            const email = line.trim();
            // Basic email validation (can be more sophisticated)
            if (!email.includes('@') || !email.includes('.')) {
              throw new Error(`Invalid email format found: ${email}`);
            }
            return email;
          }).filter(Boolean);
          resolve(emails);
        } catch (error) {
          reject(new Error("Failed to parse CSV file."));
        }
      };
      reader.onerror = () => reject(new Error("Failed to read file."));
      reader.readAsText(file);
    });
  };

  const createEvent = useMutation(api.events.create);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.description || !formData.startDate || !formData.endDate || !formData.startTime || !formData.endTime || !formData.location || !formData.registrationOpenDate || !formData.registrationCloseDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`).getTime();
    const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`).getTime();
    const regOpenDate = new Date(formData.registrationOpenDate).getTime();
    const regCloseDate = new Date(formData.registrationCloseDate).getTime();

    if (startDateTime <= Date.now()) {
      toast.error("Event start date must be in the future");
      return;
    }
    if (endDateTime <= startDateTime) {
      toast.error("Event end date/time must be after start date/time");
      return;
    }
    if (regCloseDate <= regOpenDate) {
      toast.error("Registration close date must be after open date");
      return;
    }

    try {
      let featuredImageStorageId;
      if (featuredImageFile) {
        const postUrl = await generateUploadUrl();
        const result = await fetch(postUrl, {
          method: "POST",
          headers: { "Content-Type": featuredImageFile.type },
          body: featuredImageFile,
        });
        const { storageId } = await result.json();
        featuredImageStorageId = storageId;
      }

      await createEvent({
        // Map to new schema fields
        title: formData.title,
        description: formData.description,
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: formData.startTime,
        endTime: formData.endTime,
        price: Number(formData.price) || 0,
        level: formData.level as EventLevel,
        imageUrl: formData.imageUrl,
        totalTickets: Number(formData.totalTickets) || 0,
        allowedAttendeeTypes: formData.attendees, // Ensure this matches schema (e.g., array of strings)
        category: formData.category,
        featuredImageStorageId: featuredImageStorageId, // From file upload
        // organizerId: currentUser._id, // Set on backend based on authenticated user
        location: formData.location,
        registrationOpenDate: String(regOpenDate),
        registrationCloseDate: String(regCloseDate),
        exemptedEmails: parsedExemptedEmails.length > 0 ? parsedExemptedEmails : undefined,
        // Add other fields from your spec like total capacity (totalTickets)
      });

      toast.success("Event created successfully!");
      setFormData({
        title: "",
        description: "",
        startDate: "", endDate: "", startTime: "", endTime: "",
        price: 0, level: eventLevels[0], attendees: [],
        location: "", imageUrl: "", registrationOpenDate: "", registrationCloseDate: "",
        category: categories[0], totalTickets: 0,
      });
      setParsedExemptedEmails([]);
      setFeaturedImageFile(null);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create event");
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        attendees: checked
          ? [...prev.attendees, value]
          : prev.attendees.filter(item => item !== value),
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Create New Event</h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          <EventFormFields
            formData={formData}
            onFormChange={handleChange}
            featuredImageFile={featuredImageFile}
            onFeaturedImageDrop={handleFeaturedImageDrop}
            imageDropzoneId="create-event-image-dropzone"
            exemptedEmailsFile={exemptedEmailsFile}
            onExemptedEmailsDrop={handleExemptedEmailsDrop}
            onExemptedEmailsParsed={handleExemptedEmailsParsed}
            parsedEmailsCount={parsedExemptedEmails.length}
            parseEmailsCSV={parseEmailsCSV}
            csvDropzoneId="create-event-csv-dropzone"
            eventLevels={eventLevels}
            allowedAttendeeTypes={allowedAttendeeTypes}
            categories={categories}
          />

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
          >
            Create Event
          </button>
        </form>
      </div>
    </div>
  );
}
