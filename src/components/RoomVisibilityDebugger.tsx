import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { Id } from "../../convex/_generated/dataModel";

interface RoomVisibilityDebuggerProps {
  eventId: Id<"events">;
  attendeeType?: string;
}

export function RoomVisibilityDebugger({ eventId, attendeeType }: RoomVisibilityDebuggerProps) {
  const [selectedHotelId, setSelectedHotelId] = useState<Id<"hotels"> | null>(null);
  
  const hotels = useQuery(api.hotels.getAccommodationsByEvent, { eventId });
  const debugData = useQuery(
    api.hotels.debugRoomVisibility,
    selectedHotelId ? { hotelId: selectedHotelId, attendeeType } : "skip"
  );

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-semibold text-yellow-800 mb-3">🐛 Room Visibility Debugger</h3>
      <p className="text-sm text-yellow-700 mb-3">
        This debug panel helps identify room visibility issues. Remove this component in production.
      </p>
      
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-yellow-800">Select Hotel to Debug:</label>
          <select
            value={selectedHotelId || ""}
            onChange={(e) => setSelectedHotelId(e.target.value as Id<"hotels"> || null)}
            className="w-full p-2 border rounded-md"
          >
            <option value="">Select a hotel...</option>
            {hotels?.map((hotel) => (
              <option key={hotel._id} value={hotel._id}>
                {hotel.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <p className="text-sm text-yellow-800">
            <strong>Current Attendee Type:</strong> {attendeeType || "Not specified"}
          </p>
        </div>

        {debugData && (
          <div>
            <h4 className="font-medium text-yellow-800 mb-2">Room Visibility Analysis:</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {debugData.map((room) => (
                <div
                  key={room._id}
                  className={`p-2 rounded border ${
                    room.isVisibleToAttendeeType
                      ? "bg-green-50 border-green-200"
                      : "bg-red-50 border-red-200"
                  }`}
                >
                  <div className="text-sm">
                    <strong>Room:</strong> {room.roomNumber} |{" "}
                    <strong>Capacity:</strong> {room.capacity} |{" "}
                    <strong>Price:</strong> GH₵{room.pricePerBedPerDay}
                  </div>
                  <div className="text-xs mt-1">
                    <strong>Visible To:</strong>{" "}
                    {room.visibleTo && room.visibleTo.length > 0
                      ? room.visibleTo.join(", ")
                      : "All attendee types (no restrictions)"}
                  </div>
                  <div className="text-xs mt-1">
                    <strong>Visible to {attendeeType || "current user"}:</strong>{" "}
                    <span
                      className={
                        room.isVisibleToAttendeeType ? "text-green-600" : "text-red-600"
                      }
                    >
                      {room.isVisibleToAttendeeType ? "✅ YES" : "❌ NO"}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {debugData && debugData.length === 0 && (
          <div className="text-sm text-yellow-700">
            No rooms found for this hotel. This could indicate:
            <ul className="list-disc list-inside mt-1">
              <li>No rooms have been created for this hotel</li>
              <li>All rooms are filtered out by visibility settings</li>
              <li>Database query issue</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
