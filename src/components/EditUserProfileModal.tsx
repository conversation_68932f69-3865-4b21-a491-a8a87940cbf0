import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { UserProfileFormData, formOptions } from "../types/form";
import { FormField } from "./forms/FormField";
import { useUserProfileForm } from "@/hooks/useUserProfileForm";

interface UserProfileData {
  _id: Id<"users">;
  email: string; // Display only
  name?: string | null;
  memberNumber?: string | null;
  organizationName?: string | null;
  title?: string | null;
  gender?: string | null;
  dateOfBirth?: string | null;
  maritalStatus?: string | null;
  otherMaritalStatusDetails?: string | null;
  commanderyAuxiliaryId?: Id<"commanderiesAuxiliaries"> | null;
  // currentOffice?: string | null;
  yearOfInitiation?: number | null;
  rank?: string | null;
  officeId?: Id<"ksjiOffices"> | null;
  educationalBackground?: string | null;
  otherEducationalBackgroundDetails?: string | null;
  employmentStatus?: string | null;
  otherEmploymentStatusDetails?: string | null;
  careerProfession?: string | null;
  currentPlaceOfWork?: string | null;
  otherSkillsExpertise?: string | null;
  grandId?: Id<"grands"> | null;
  districtId?: Id<"districts"> | null;
  phoneNumber?: string | null;
}

interface EditUserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserProfileData;
  onUpdate: (userId: Id<"users">, updates: any) => void;
}

export function EditUserProfileModal({
  isOpen,
  onClose,
  user,
  onUpdate,
}: EditUserProfileModalProps) {
const [isSubmitting, setIsSubmitting] = useState(false);
  const updateUserProfileByAdmin = useMutation(api.users.updateUserProfileByAdmin);

  // Fetch hierarchical data
  const grands = useQuery(api.data.listGrands) || [];
  const districts = useQuery(
    api.data.listDistrictsByGrand,
    user.grandId ? { grandId: user.grandId } : "skip"
  ) || [];
  const commanderies = useQuery(
    api.data.listCommanderiesByDistrict,
    user.districtId ? { districtId: user.districtId } : "skip"
  ) || [];
  const offices = useQuery(api.data.listOffices) || [];

  // Initialize form with user data
  const initialData: UserProfileFormData = {
    name: user.name || "",
    memberNumber: user.memberNumber || "",
    organizationName: user.organizationName || "",
    title: user.title || "",
    gender: user.gender || "",
    dateOfBirth: user.dateOfBirth || "",
    maritalStatus: user.maritalStatus || "",
    otherMaritalStatusDetails: user.otherMaritalStatusDetails || "",
    commanderyAuxiliaryId: user.commanderyAuxiliaryId || "",
    // currentOffice: user.currentOffice || "",
    yearOfInitiation: user.yearOfInitiation?.toString() || "",
    rank: user.rank || "",
    officeId: user.officeId || "",
    educationalBackground: user.educationalBackground || "",
    otherEducationalBackgroundDetails: user.otherEducationalBackgroundDetails || "",
    employmentStatus: user.employmentStatus || "",
    otherEmploymentStatusDetails: user.otherEmploymentStatusDetails || "",
    careerProfession: user.careerProfession || "",
    currentPlaceOfWork: user.currentPlaceOfWork || "",
    otherSkillsExpertise: user.otherSkillsExpertise || "",
    grandId: user.grandId?.toString() || "",
    districtId: user.districtId?.toString() || "",
    phoneNumber: user.phoneNumber || ""
  };

  const {
    formData,
    handleChange,
    handleOtherFieldChange,
    setFieldValue,
    errors,
  } = useUserProfileForm(initialData);

  // Handle hierarchical data changes
  useEffect(() => {
    if (formData.grandId && formData.districtId) {
      // If grand changes, clear district and commandery
      if (formData.grandId !== initialData.grandId) {
        setFieldValue('districtId', '');
        setFieldValue('commanderyAuxiliaryId', '');
      }
      // If district changes, clear commandery
      else if (formData.districtId !== initialData.districtId) {
        setFieldValue('commanderyAuxiliaryId', '');
      }
    }
  }, [formData.grandId, formData.districtId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const yearOfInitiationNumber = formData.yearOfInitiation ?
      Number(formData.yearOfInitiation) : undefined;

    if (formData.yearOfInitiation &&
      (isNaN(yearOfInitiationNumber!) ||
        yearOfInitiationNumber! < 1900 ||
        yearOfInitiationNumber! > new Date().getFullYear() + 5)) {
      toast.error("Please enter a valid year for Year of Initiation (1900 - current year + 5).");
      setIsSubmitting(false);
      return;
    }

    try {
      const updates: Record<string, any> = {};

      // Only include fields that have changed
      Object.keys(formData).forEach((key) => {
        if (formData[key as keyof UserProfileFormData] !== undefined &&
          formData[key as keyof UserProfileFormData] !== initialData[key as keyof UserProfileFormData]) {
          updates[key] = formData[key as keyof UserProfileFormData] || undefined;
        }
      });

      if (Object.keys(updates).length > 0) {
        await updateUserProfileByAdmin({
          userId: user._id,
          ...updates,
          yearOfInitiation: yearOfInitiationNumber,
          // Convert string IDs back to Convex IDs
          grandId: formData.grandId ? formData.grandId as Id<"grands"> : undefined,
          districtId: formData.districtId ? formData.districtId as Id<"districts"> : undefined,
        });

        toast.success("User profile updated successfully!");
        onUpdate(user._id, updates);
        onClose();
      } else {
        toast.info("No changes detected");
        onClose();
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update user profile.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-md p-6 md:p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Edit User: <span className="font-normal">{user.email}</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            disabled={isSubmitting}
            aria-label="Close modal"
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
              Personal Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <FormField
                  label="Full Name"
                  name="name"
                  type="text"
                  value={formData.name || ''}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <FormField
                  label="Title"
                  name="title"
                  type="select"
                  value={formData.title || ''}
                  onChange={handleChange}
                  options={formOptions.titles}
                />
              </div>

              <div>
                <FormField
                  label="Gender"
                  name="gender"
                  type="select"
                  value={formData.gender || ''}
                  onChange={handleChange}
                  options={formOptions.genders}
                />
              </div>

              <div>
                <FormField
                  label="Date of Birth"
                  name="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <FormField
                  label="Marital Status"
                  name="maritalStatus"
                  type="select"
                  value={formData.maritalStatus || ''}
                  onChange={handleChange}
                  options={formOptions.maritalStatuses}
                  showOtherField={(val) => val === 'Other'}
                  otherFieldName="otherMaritalStatusDetails"
                  otherFieldValue={formData.otherMaritalStatusDetails}
                  onOtherFieldChange={handleOtherFieldChange}
                />
              </div>

              <div>
                <FormField
                  label="Phone Number"
                  name="phoneNumber"
                  type="tel"
                  value={formData.phoneNumber || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>

          {/* KSJI Information Section */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
              KSJI Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <FormField
                  label="Member Number"
                  name="memberNumber"
                  type="text"
                  value={formData.memberNumber || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <FormField
                  label="Organization Name"
                  name="organizationName"
                  type="text"
                  value={formData.organizationName || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <FormField
                  label="Year of Initiation"
                  name="yearOfInitiation"
                  type="number"
                  value={formData.yearOfInitiation || ''}
                  onChange={handleChange}
                  min="1900"
                  max={new Date().getFullYear() + 5}
                />
              </div>

              <div>
                <FormField
                  label="Rank"
                  name="rank"
                  type="select"
                  value={formData.rank || ''}
                  onChange={handleChange}
                  options={formOptions.ranks}
                />
              </div>

              {/* Hierarchical Data */}
              <div>
                <FormField
                  label="Grand"
                  name="grandId"
                  type="select"
                  value={formData.grandId || ''}
                  onChange={handleChange}
                  options={['', ...grands.map(g => g._id)]}
                  optionLabels={['Select Grand', ...grands.map(g => g.name)]}
                />
              </div>

              {formData.grandId && (
                <div>
                  <FormField
                    label="District"
                    name="districtId"
                    type="select"
                    value={formData.districtId || ''}
                    onChange={handleChange}
                    options={['', ...districts.map(d => d._id)]}
                    optionLabels={['Select District', ...districts.map(d => d.name)]}
                  />
                </div>
              )}

              {formData.districtId && (
                <div>
                  <FormField
                    label="Commandery/Auxiliary"
                    name="commanderyAuxiliaryId"
                    type="select"
                    value={formData.commanderyAuxiliaryId || ''}
                    onChange={handleChange}
                    options={['', ...commanderies.map(c => c._id)]}
                    optionLabels={['Select Commandery/Auxiliary', ...commanderies.map(c => c.name)]}
                  />
                </div>
              )}

              <div>
                <FormField
                  label="Current Office"
                  name="officeId"
                  type="select"
                  value={formData.officeId || ''}
                  onChange={handleChange}
                  options={['', ...offices.map(o => o._id)]}
                  optionLabels={['Select Office', ...offices.map(o => o.name)]}
                />
              </div>
            </div>
          </div>

          {/* Education & Employment Section */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
              Education & Employment
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <FormField
                  label="Educational Background"
                  name="educationalBackground"
                  type="select"
                  value={formData.educationalBackground || ''}
                  onChange={handleChange}
                  options={formOptions.educationalBackgrounds}
                  showOtherField={(val) => val === 'Other'}
                  otherFieldName="otherEducationalBackgroundDetails"
                  otherFieldValue={formData.otherEducationalBackgroundDetails}
                  onOtherFieldChange={handleOtherFieldChange}
                />
              </div>

              <div>
                <FormField
                  label="Employment Status"
                  name="employmentStatus"
                  type="select"
                  value={formData.employmentStatus || ''}
                  onChange={handleChange}
                  options={formOptions.employmentStatuses}
                  showOtherField={(val) => val === 'Other'}
                  otherFieldName="otherEmploymentStatusDetails"
                  otherFieldValue={formData.otherEmploymentStatusDetails}
                  onOtherFieldChange={handleOtherFieldChange}
                />
              </div>

              <div>
                <FormField
                  label="Career/Profession"
                  name="careerProfession"
                  type="text"
                  value={formData.careerProfession || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <FormField
                  label="Current Place of Work"
                  name="currentPlaceOfWork"
                  type="text"
                  value={formData.currentPlaceOfWork || ''}
                  onChange={handleChange}
                />
              </div>

              <div className="md:col-span-2">
                <FormField
                  label="Other Skills/Expertise"
                  name="otherSkillsExpertise"
                  type="textarea"
                  value={formData.otherSkillsExpertise || ''}
                  onChange={handleChange}
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}