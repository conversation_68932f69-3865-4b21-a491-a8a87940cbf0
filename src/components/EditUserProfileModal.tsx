import { useState, useEffect } from "react";
import { UserTitle, Gender, MaritalStatus, UserRank } from "../types/user";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";

interface UserProfileData {
  _id: Id<"users">;
  name?: string | null;
  email: string; // Display only
  memberNumber?: string | null;
  organizationName?: string | null;
  // Add all other fields from UserProfile.tsx's FormData
  title?: string | null; // Assuming UserTitle is a string union
  gender?: string | null; // Assuming Gender is a string union
  dateOfBirth?: string | null;
  maritalStatus?: string | null; // Assuming MaritalStatus is a string union
  otherMaritalStatusDetails?: string | null;
  commanderyAuxiliaryId?: string | null;
  currentOffice?: string | null;
  yearOfInitiation?: number | null;
  rank?: string | null; // Assuming Rank is a string union
  educationalBackground?: string | null;
  employmentStatus?: string | null; // Assuming EmploymentStatus is a string union
  otherEmploymentStatusDetails?: string | null;
  careerProfession?: string | null;
  currentPlaceOfWork?: string | null;
  otherSkillsExpertise?: string | null;
}

interface UserProfileFormData {
  name?: string;
  memberNumber?: string;
  organizationName?: string;
  title?: string;
  gender?: string;
  dateOfBirth?: string;
  maritalStatus?: string;
  otherMaritalStatusDetails?: string;
  commanderyAuxiliaryId?: string;
  currentOffice?: string;
  yearOfInitiation?: string;
  rank?: string;
  educationalBackground?: string;
  employmentStatus?: string;
  otherEmploymentStatusDetails?: string;
  careerProfession?: string;
  currentPlaceOfWork?: string;
  otherSkillsExpertise?: string;
}

interface EditUserProfileModalProps {
  userToEdit: UserProfileData;
  onClose: () => void;
  onSuccess: () => void;
}

// Re-define options here or import from a shared location
const titles: UserTitle[] = ["Mr.", "Mrs.", "Ms.", "Dr.", "Prof.", "Ing.", "Hon.", "Rev.", "Ps.", "Aps.", "ESQ.", "Suv.", "Chief", "Nana"];
const genders: Gender[] = ["Male", "Female"];
const maritalStatuses: MaritalStatus[] = ["Single", "Married", "Divorced", "Widowed", "Other"];
const employmentStatuses = ["Employed", "Self-Employed", "Unemployed", "Student", "Other"];
const rankOptions: UserRank[] = ["1st Lieutenant (1Lt.)", "2nd Lieutenant (2Lt.)", "Captain (Capt.)", "Major (Maj.)", "Lieutenant Colonel (LtCol.)", "Colonel (Col.)", "Brigadier General (BGen.)", "Major General (MGen.)", "Lieutenant General (Lt. Gen.)", "General"];

export function EditUserProfileModal({ userToEdit, onClose, onSuccess }: EditUserProfileModalProps) {
  const [formData, setFormData] = useState<UserProfileFormData>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const updateUserProfileByAdmin = useMutation(api.users.updateUserProfileByAdmin);

  useEffect(() => {
    if (userToEdit) {
      setFormData({
        name: userToEdit.name || "",
        memberNumber: userToEdit.memberNumber || "",
        organizationName: userToEdit.organizationName || "",
        title: userToEdit.title || "",
        gender: userToEdit.gender || "",
        dateOfBirth: userToEdit.dateOfBirth || "",
        maritalStatus: userToEdit.maritalStatus || "",
        otherMaritalStatusDetails: userToEdit.otherMaritalStatusDetails || "",
        commanderyAuxiliaryId: userToEdit.commanderyAuxiliaryId || "",
        currentOffice: userToEdit.currentOffice || "",
        yearOfInitiation: userToEdit.yearOfInitiation?.toString() || "",
        rank: userToEdit.rank || "",
        educationalBackground: userToEdit.educationalBackground || "",
        employmentStatus: userToEdit.employmentStatus || "",
        otherEmploymentStatusDetails: userToEdit.otherEmploymentStatusDetails || "",
        careerProfession: userToEdit.careerProfession || "",
        currentPlaceOfWork: userToEdit.currentPlaceOfWork || "",
        otherSkillsExpertise: userToEdit.otherSkillsExpertise || "",
      });
    }
  }, [userToEdit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    const yearOfInitiationNumber = formData.yearOfInitiation ? Number(formData.yearOfInitiation) : undefined;
    if (formData.yearOfInitiation && (isNaN(yearOfInitiationNumber!) || yearOfInitiationNumber! < 1900 || yearOfInitiationNumber! > new Date().getFullYear() + 5 )) {
        toast.error("Please enter a valid year for Year of Initiation.");
        setIsSubmitting(false);
        return;
    }
    
    try {
      await updateUserProfileByAdmin({
        userId: userToEdit._id,
        name: formData.name || undefined, // Send empty string to clear, or current value
        memberNumber: formData.memberNumber || undefined,
        organizationName: formData.organizationName || undefined,
        title: (formData.title as UserTitle) || undefined,
        gender: (formData.gender as Gender) || undefined,
        dateOfBirth: formData.dateOfBirth || undefined,
        maritalStatus: formData.maritalStatus as MaritalStatus | undefined,
        otherMaritalStatusDetails: formData.otherMaritalStatusDetails || undefined,
        commanderyAuxiliaryId: formData.commanderyAuxiliaryId || undefined,
        currentOffice: formData.currentOffice || undefined,
        yearOfInitiation: yearOfInitiationNumber,
        rank: formData.rank as UserRank || undefined,
        educationalBackground: formData.educationalBackground || undefined,
        employmentStatus: formData.employmentStatus || undefined,
        otherEmploymentStatusDetails: formData.otherEmploymentStatusDetails || undefined,
        careerProfession: formData.careerProfession || undefined,
        currentPlaceOfWork: formData.currentPlaceOfWork || undefined,
        otherSkillsExpertise: formData.otherSkillsExpertise || undefined,
      });
      toast.success("User profile updated successfully!");
      onSuccess();
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update user profile.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!userToEdit) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-md p-6 md:p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Edit User: <span className="font-normal">{userToEdit.email}</span></h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 text-2xl" disabled={isSubmitting}>
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input type="text" name="name" id="name" value={formData.name} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">Title</label>
              <select name="title" id="title" value={formData.title} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Title</option>
                {titles.map(t => <option key={t} value={t}>{t}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
              <select name="gender" id="gender" value={formData.gender} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Gender</option>
                {genders.map(g => <option key={g} value={g}>{g}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
              <input type="date" name="dateOfBirth" id="dateOfBirth" value={formData.dateOfBirth} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
              <label htmlFor="maritalStatus" className="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
              <select name="maritalStatus" id="maritalStatus" value={formData.maritalStatus} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Marital Status</option>
                {maritalStatuses.map(s => <option key={s} value={s}>{s}</option>)}
              </select>
            </div>
            {formData.maritalStatus === "Other" && (
              <div className="md:col-span-2">
                <label htmlFor="otherMaritalStatusDetails" className="block text-sm font-medium text-gray-700 mb-1">Other Marital Status Details</label>
                <input type="text" name="otherMaritalStatusDetails" id="otherMaritalStatusDetails" value={formData.otherMaritalStatusDetails} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
              </div>
            )}
            <div>
              <label htmlFor="memberNumber" className="block text-sm font-medium text-gray-700 mb-1">Member Number</label>
              <input type="text" name="memberNumber" id="memberNumber" value={formData.memberNumber} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
              <label htmlFor="organizationName" className="block text-sm font-medium text-gray-700 mb-1">Organization Name</label>
              <input type="text" name="organizationName" id="organizationName" value={formData.organizationName} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
             <div>
              <label htmlFor="commanderyAuxiliaryId" className="block text-sm font-medium text-gray-700 mb-1">Commandery/Auxiliary ID</label>
              <input type="text" name="commanderyAuxiliaryId" id="commanderyAuxiliaryId" value={formData.commanderyAuxiliaryId} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
              <label htmlFor="currentOffice" className="block text-sm font-medium text-gray-700 mb-1">Current Office</label>
              <input type="text" name="currentOffice" id="currentOffice" value={formData.currentOffice} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
              <label htmlFor="yearOfInitiation" className="block text-sm font-medium text-gray-700 mb-1">Year of Initiation</label>
              <input type="number" name="yearOfInitiation" id="yearOfInitiation" value={formData.yearOfInitiation} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="YYYY" />
            </div>
            <div>
              <label htmlFor="rank" className="block text-sm font-medium text-gray-700 mb-1">Rank</label>
              <select name="rank" id="rank" value={formData.rank} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Rank</option>
                {rankOptions.map(r => <option key={r} value={r}>{r}</option>)}
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="educationalBackground" className="block text-sm font-medium text-gray-700 mb-1">Educational Background</label>
            <textarea name="educationalBackground" id="educationalBackground" value={formData.educationalBackground} onChange={handleChange} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="employmentStatus" className="block text-sm font-medium text-gray-700 mb-1">Employment Status</label>
              <select name="employmentStatus" id="employmentStatus" value={formData.employmentStatus} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Employment Status</option>
                {employmentStatuses.map(s => <option key={s} value={s}>{s}</option>)}
              </select>
            </div>
            {formData.employmentStatus === "Other" && (
              <div>
                <label htmlFor="otherEmploymentStatusDetails" className="block text-sm font-medium text-gray-700 mb-1">Other Employment Status Details</label>
                <input type="text" name="otherEmploymentStatusDetails" id="otherEmploymentStatusDetails" value={formData.otherEmploymentStatusDetails} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
              </div>
            )}
            <div>
              <label htmlFor="careerProfession" className="block text-sm font-medium text-gray-700 mb-1">Career/Profession</label>
              <input type="text" name="careerProfession" id="careerProfession" value={formData.careerProfession} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
              <label htmlFor="currentPlaceOfWork" className="block text-sm font-medium text-gray-700 mb-1">Current Place of Work</label>
              <input type="text" name="currentPlaceOfWork" id="currentPlaceOfWork" value={formData.currentPlaceOfWork} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>
          </div>
          <div>
            <label htmlFor="otherSkillsExpertise" className="block text-sm font-medium text-gray-700 mb-1">Other Skills/Expertise</label>
            <textarea name="otherSkillsExpertise" id="otherSkillsExpertise" value={formData.otherSkillsExpertise} onChange={handleChange} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" disabled={isSubmitting} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
              {isSubmitting ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}