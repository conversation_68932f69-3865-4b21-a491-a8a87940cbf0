import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";

interface ModernCardProps {
  children: ReactNode;
  className?: string;
  gradient?: string;
  icon?: LucideIcon;
  title?: string;
  subtitle?: string;
  hover?: boolean;
  glow?: boolean;
}

export function ModernCard({ 
  children, 
  className = "", 
  gradient = "from-white/10 to-white/5",
  icon: Icon,
  title,
  subtitle,
  hover = true,
  glow = false
}: ModernCardProps) {
  return (
    <div className={`
      backdrop-blur-xl bg-gradient-to-br ${gradient} 
      border border-white/20 rounded-2xl shadow-xl
      ${hover ? 'hover:shadow-2xl hover:scale-105 hover:border-white/30' : ''}
      ${glow ? 'shadow-blue-500/20' : ''}
      transition-all duration-300 relative overflow-hidden group
      ${className}
    `}>
      {/* Background glow effect */}
      {glow && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      )}
      
      {/* Header */}
      {(title || Icon) && (
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center space-x-3">
            {Icon && (
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Icon className="w-5 h-5 text-white" />
              </div>
            )}
            {title && (
              <div>
                <h3 className="text-lg font-semibold text-white">{title}</h3>
                {subtitle && <p className="text-white/60 text-sm">{subtitle}</p>}
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10 p-6">
        {children}
      </div>
      
      {/* Floating particles for extra flair */}
      <div className="absolute top-4 right-4 w-2 h-2 bg-white/20 rounded-full animate-pulse"></div>
      <div className="absolute bottom-4 left-4 w-1 h-1 bg-white/30 rounded-full animate-ping"></div>
    </div>
  );
}

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  gradient?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export function StatsCard({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  gradient = "from-blue-500 to-purple-600",
  trend 
}: StatsCardProps) {
  return (
    <ModernCard className="p-6" hover glow>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-white/60 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
          {subtitle && <p className="text-white/50 text-xs mt-1">{subtitle}</p>}
          
          {trend && (
            <div className={`flex items-center mt-2 text-xs ${
              trend.isPositive ? 'text-green-400' : 'text-red-400'
            }`}>
              <span>{trend.isPositive ? '↗' : '↘'}</span>
              <span className="ml-1">{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
        
        <div className={`w-12 h-12 bg-gradient-to-r ${gradient} rounded-xl flex items-center justify-center shadow-lg`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </ModernCard>
  );
}

interface ActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  onClick: () => void;
  gradient?: string;
  buttonText?: string;
}

export function ActionCard({ 
  title, 
  description, 
  icon: Icon, 
  onClick, 
  gradient = "from-green-500 to-emerald-600",
  buttonText = "Get Started"
}: ActionCardProps) {
  return (
    <ModernCard className="p-6 cursor-pointer" onClick={onClick} hover glow>
      <div className="text-center">
        <div className={`w-16 h-16 bg-gradient-to-r ${gradient} rounded-2xl flex items-center justify-center shadow-lg mx-auto mb-4`}>
          <Icon className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
        <p className="text-white/60 text-sm mb-4">{description}</p>
        
        <button className={`
          w-full py-2 px-4 bg-gradient-to-r ${gradient} 
          text-white font-medium rounded-xl 
          hover:shadow-lg transform hover:scale-105 
          transition-all duration-300
        `}>
          {buttonText}
        </button>
      </div>
    </ModernCard>
  );
}
