import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Plus, Bed, Layers } from "lucide-react";
import { Id } from "../../convex/_generated/dataModel";

interface AdminAccommodationManagementProps {
  eventId?: Id<"events">;
}

export function AdminAccommodationManagement({ eventId }: AdminAccommodationManagementProps) {
  const [name, setName] = useState("");
  const [location, setLocation] = useState("");
  const [selectedAccommodationId, setSelectedAccommodationId] = useState<Id<"hotels"> | null>(null);
  const [currentManagedEventId, setCurrentManagedEventId] = useState<Id<"events"> | undefined>(eventId);

  // Get current user first
  const currentUser = useQuery(api.auth.loggedInUser);

  // Fetch user's events if no specific eventId is provided, to allow selection
  // Only query if we know the user is loaded and is an organizer
  const myEvents = useQuery(
    api.events.myEvents,
    currentUser !== undefined && currentUser?.isEventOrganizerGlobal === true && !currentManagedEventId ? {} : "skip"
  );

  const hotels = useQuery(api.hotels.getAccommodationsByEvent, currentManagedEventId ? { eventId: currentManagedEventId } : "skip");
  const createAccommodation = useMutation(api.hotels.createAccommodation);

  // Get the current event details
  const getCurrentEvent = () => {
    if (!currentManagedEventId) return null;
    const event = myEvents?.find(e => e._id === currentManagedEventId);
    console.log("� Current user:", currentUser);
    console.log("🔑 User is organizer:", currentUser?.isEventOrganizerGlobal);
    console.log("�🔍 Looking for event:", currentManagedEventId, "in events:", myEvents?.map(e => ({ id: e._id, title: e.title })));
    console.log("📋 Found event:", event);
    return event;
  };

  const currentEvent = getCurrentEvent();

  const handleCreateAccommodation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentManagedEventId) {
      toast.error("Please select an event first to create an accommodation for.");
      return;
    }
    try {
      await createAccommodation({ name, location, eventId: currentManagedEventId });
      toast.success("Accommodation created successfully");
      setName("");
      setLocation("");
    } catch (error) {
      toast.error("Failed to create accommodation");
    }
  };

  useEffect(() => {
    // If the eventId prop changes, update the internal state
    console.log("🔄 EventId prop changed:", eventId);
    setCurrentManagedEventId(eventId);
  }, [eventId]);

  // Show loading if user or events are still loading
  if (!currentManagedEventId && (currentUser === undefined || myEvents === undefined)) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-2">Loading events...</p>
      </div>
    );
  }

  // Check if user has organizer permissions
  if (!currentManagedEventId && currentUser && !currentUser.isEventOrganizerGlobal) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <h3 className="text-lg font-semibold mb-4">Accommodation Management</h3>
        <p className="text-gray-600">You don't have permission to manage accommodations. Contact an administrator for access.</p>
      </div>
    );
  }

  if (!currentManagedEventId && myEvents && myEvents.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <h3 className="text-lg font-semibold mb-4">Accommodation Management</h3>
        <p className="text-gray-600">You have no events to manage accommodations for. Please create an event first.</p>
      </div>
    );
  }

  if (!currentManagedEventId && myEvents && myEvents.length > 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Select an Event to Manage Accommodations</h3>
        <div className="space-y-2">
          {myEvents.map((event) => (
            <button
              key={event._id}
              onClick={() => {
                console.log("🎯 Selected event:", event._id, event.title);
                setCurrentManagedEventId(event._id as Id<"events">);
              }}
              className="w-full text-left p-3 border rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {event.title}
            </button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {currentManagedEventId && (
        <>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">
              Create New Accommodation for "{
                currentEvent?.title ||
                (myEvents === undefined ? 'Loading...' : 'Selected Event')
              }"
            </h3>
            <form onSubmit={handleCreateAccommodation} className="flex gap-4 items-end">
              <div className="flex-1">
                <label htmlFor="accommodationName" className="block text-sm font-medium">Accommodation Name</label>
                <input id="accommodationName" type="text" value={name} onChange={(e) => setName(e.target.value)} className="w-full p-2 border rounded-md" required />
              </div>
              <div className="flex-1">
                <label htmlFor="accommodationLocation" className="block text-sm font-medium">Location</label>
                <input id="accommodationLocation" type="text" value={location} onChange={(e) => setLocation(e.target.value)} className="w-full p-2 border rounded-md" required />
              </div>
              <button type="submit" className="flex items-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                <Plus className="w-4 h-4 mr-2" /> Create Accommodation
              </button>
            </form>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Manage Accommodations</h3>
            {hotels === undefined && <p>Loading accommodations...</p>}
            {hotels && hotels.length === 0 && <p className="text-gray-500">No accommodations found for this event.</p>}
            <div className="space-y-4">
              {hotels?.map((accommodation) => (
                <div key={accommodation._id} className="border p-4 rounded-md">
                  <h4 className="font-semibold">{accommodation.name}</h4>
                  <p className="text-sm text-gray-500">{accommodation.location}</p>
                  <button
                    onClick={() => setSelectedAccommodationId(prevId => prevId === accommodation._id ? null : accommodation._id)}
                    className={`text-sm mt-2 px-3 py-1 rounded ${selectedAccommodationId === accommodation._id ? "bg-blue-500 text-white" : "bg-gray-200 text-blue-700 hover:bg-gray-300"}`}
                  >
                    {selectedAccommodationId === accommodation._id ? "Hide Rooms" : "Manage Rooms"}
                  </button>
                  {selectedAccommodationId === accommodation._id && (
                    <AdminRoomManagement accommodationId={accommodation._id} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

interface AdminRoomManagementProps {
  accommodationId: Id<"hotels">;
}

function AdminRoomManagement({ accommodationId: hotelId }: AdminRoomManagementProps) {
  // State for single room creation
  const [roomNumber, setRoomNumber] = useState("");
  const [capacity, setCapacity] = useState(1);
  const [pricePerBedPerDay, setPricePerBedPerDay] = useState(0);
  const [visibleTo, setVisibleTo] = useState<string[]>([]);
  const [editingRoom, setEditingRoom] = useState<any | null>(null);
  const [editingPrice, setEditingPrice] = useState<number>(0);

  // State for bulk room creation
  const [bulkPrefix, setBulkPrefix] = useState("Room ");
  const [bulkStartNumber, setBulkStartNumber] = useState(101);
  const [bulkNumberOfRooms, setBulkNumberOfRooms] = useState(5);
  const [bulkSuffix, setBulkSuffix] = useState("");
  const [bulkCapacity, setBulkCapacity] = useState(2);
  const [bulkPricePerBedPerDay, setBulkPricePerBedPerDay] = useState(100);
  const [bulkVisibleTo, setBulkVisibleTo] = useState<string[]>([]);
  const [previewRoomNames, setPreviewRoomNames] = useState<string[]>([]);
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false);

  const rooms = useQuery(api.hotels.getRoomsByHotel, { hotelId });
  const addRoom = useMutation(api.hotels.addRoom);
  const addRoomsInBulk = useMutation(api.hotels.addRoomsInBulk);
  const updateRoomVisibility = useMutation(api.hotels.updateRoomVisibility);
  const updateRoomPrice = useMutation(api.hotels.updateRoomPrice);

  const handleAddRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addRoom({ hotelId, roomNumber, capacity, pricePerBedPerDay, visibleTo: visibleTo as any });
      toast.success("Room added successfully");
      setRoomNumber("");
      setCapacity(1);
      setPricePerBedPerDay(0);
      setVisibleTo([]);
    } catch (error) {
      toast.error("Failed to add room");
    }
  };

  const handleUpdateVisibility = async () => {
    if (!editingRoom) return;

    try {
      await updateRoomVisibility({
        roomId: editingRoom._id,
        visibleTo: visibleTo as any,
      });
      toast.success("Room visibility updated successfully");
      setEditingRoom(null);
      setVisibleTo([]);
    } catch (error) {
      toast.error("Failed to update room visibility");
    }
  };

  const handleUpdatePrice = async () => {
    if (!editingRoom) return;

    try {
      await updateRoomPrice({
        roomId: editingRoom._id,
        pricePerBedPerDay: editingPrice,
      });
      toast.success("Room price updated successfully");
      setEditingRoom(null);
      setEditingPrice(0);
    } catch (error) {
      toast.error("Failed to update room price");
    }
  };

  useEffect(() => {
    if (bulkNumberOfRooms > 0 && bulkNumberOfRooms <= 50) {
      const names = [];
      for (let i = 0; i < bulkNumberOfRooms; i++) {
        names.push(`${bulkPrefix}${bulkStartNumber + i}${bulkSuffix || ""}`);
      }
      setPreviewRoomNames(names);
    } else {
      setPreviewRoomNames([]);
    }
  }, [bulkPrefix, bulkStartNumber, bulkNumberOfRooms, bulkSuffix]);

  const handleBulkAddRooms = async (e: React.FormEvent) => {
    e.preventDefault();
    if (bulkNumberOfRooms <= 0) {
      toast.error("Number of rooms must be greater than 0.");
      return;
    }
    if (bulkNumberOfRooms > 50) {
      toast.error("Cannot create more than 50 rooms at once.");
      return;
    }
    setIsBulkSubmitting(true);
    try {
      const result = await addRoomsInBulk({
        hotelId,
        prefix: bulkPrefix,
        startNumber: bulkStartNumber,
        numberOfRooms: bulkNumberOfRooms,
        suffix: bulkSuffix,
        capacity: bulkCapacity,
        pricePerBedPerDay: bulkPricePerBedPerDay,
        visibleTo: bulkVisibleTo as any,
      });
      toast.success(result.message || `${result.count} rooms created successfully!`);
      // Reset bulk form fields
      setBulkPrefix("Room ");
      setBulkStartNumber(101);
      setBulkNumberOfRooms(5);
      setBulkSuffix("");
      setBulkCapacity(2);
      setBulkPricePerBedPerDay(100);
      setBulkVisibleTo([]);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to add rooms in bulk.");
    } finally {
      setIsBulkSubmitting(false);
    }
  };

  return (
    <div className="mt-4 pt-4 border-t">
      {/* Single Room Creation Form */}
      <div className="mb-8 p-4 border rounded-md bg-gray-50">
        <h5 className="font-semibold mb-3 text-md">Add Single Room</h5>
        <form onSubmit={handleAddRoom} className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
          <div className="md:col-span-2">
            <label htmlFor="roomNumber" className="block text-sm font-medium">Room Number</label>
            <input id="roomNumber" type="text" value={roomNumber} onChange={(e) => setRoomNumber(e.target.value)} className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <div>
            <label htmlFor="capacity" className="block text-sm font-medium">Capacity</label>
            <input id="capacity" type="number" value={capacity} onChange={(e) => setCapacity(Number(e.target.value))} min="1" className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <div>
            <label htmlFor="pricePerBedPerDay" className="block text-sm font-medium">Price/Bed/Day (GH₵)</label>
            <input id="pricePerBedPerDay" type="number" value={pricePerBedPerDay} onChange={(e) => setPricePerBedPerDay(Number(e.target.value))} min="0" className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <div>
            <label htmlFor="visibleTo" className="block text-sm font-medium">Visible To</label>
            <select
              id="visibleTo"
              multiple
              value={visibleTo}
              onChange={(e) => setVisibleTo(Array.from(e.target.selectedOptions, option => option.value))}
              className="w-full p-2 border rounded-md mt-1"
            >
              <option value="VIP">VIP</option>
              <option value="Delegates">Delegates</option>
              <option value="Observers">Observers</option>
              <option value="Guests">Guests</option>
            </select>
          </div>
          <button type="submit" className="flex items-center justify-center bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 h-10">
            <Bed className="w-4 h-4 mr-2" /> Add Room
          </button>
        </form>
      </div>

      {/* Bulk Room Creation Form */}
      <div className="mb-8 p-4 border rounded-md bg-blue-50">
        <h5 className="font-semibold mb-3 text-md flex items-center">
          <Layers className="w-5 h-5 mr-2 text-blue-600" />
          Bulk Add Rooms (Pattern-Based)
        </h5>
        <form onSubmit={handleBulkAddRooms} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkPrefix" className="block text-sm font-medium">Prefix</label>
              <input
                id="bulkPrefix"
                type="text"
                value={bulkPrefix}
                onChange={(e) => setBulkPrefix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., Room, Suite"
              />
            </div>
            <div>
              <label htmlFor="bulkSuffix" className="block text-sm font-medium">Suffix</label>
              <input
                id="bulkSuffix"
                type="text"
                value={bulkSuffix}
                onChange={(e) => setBulkSuffix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., -A, -Standard"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkStartNumber" className="block text-sm font-medium">Start Number</label>
              <input
                id="bulkStartNumber"
                type="number"
                value={bulkStartNumber}
                onChange={(e) => setBulkStartNumber(Number(e.target.value))}
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkNumberOfRooms" className="block text-sm font-medium">Number of Rooms</label>
              <input
                id="bulkNumberOfRooms"
                type="number"
                value={bulkNumberOfRooms}
                onChange={(e) => setBulkNumberOfRooms(Number(e.target.value))}
                min="1"
                max="50"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="bulkCapacity" className="block text-sm font-medium">Default Capacity (for all)</label>
              <input
                id="bulkCapacity"
                type="number"
                value={bulkCapacity}
                onChange={(e) => setBulkCapacity(Number(e.target.value))}
                min="1"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkPricePerBedPerDay" className="block text-sm font-medium">Default Price/Bed/Day (GH₵ for all)</label>
              <input
                id="bulkPricePerBedPerDay"
                type="number"
                value={bulkPricePerBedPerDay}
                onChange={(e) => setBulkPricePerBedPerDay(Number(e.target.value))}
                min="0"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkVisibleTo" className="block text-sm font-medium">Visible To (for all)</label>
              <select
                id="bulkVisibleTo"
                multiple
                value={bulkVisibleTo}
                onChange={(e) => setBulkVisibleTo(Array.from(e.target.selectedOptions, option => option.value))}
                className="w-full p-2 border rounded-md mt-1"
              >
                <option value="VIP">VIP</option>
                <option value="Delegates">Delegates</option>
                <option value="Observers">Observers</option>
                <option value="Guests">Guests</option>
              </select>
            </div>
          </div>

          {previewRoomNames.length > 0 && (
            <div className="mt-4">
              <h6 className="text-sm font-medium mb-1">Preview of Room Names:</h6>
              <div className="p-2 border rounded-md bg-white max-h-28 overflow-y-auto text-xs">
                {previewRoomNames.slice(0, 5).map(name => <div key={name}>{name}</div>)}
                {previewRoomNames.length > 5 && <div>...and {previewRoomNames.length - 5} more.</div>}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={isBulkSubmitting}
            className="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 w-full md:w-auto disabled:opacity-50"
          >
            <Layers className="w-4 h-4 mr-2" />
            {isBulkSubmitting ? "Adding..." : "Add Rooms in Bulk"}
          </button>
        </form>
      </div>

      {/* Existing Rooms List */}
      <div>
        <h5 className="font-semibold mb-2 text-md">Existing Rooms ({rooms?.length || 0})</h5>
        {rooms === undefined && <p>Loading rooms...</p>}
        {rooms && rooms.length === 0 && <p className="text-sm text-gray-500">No rooms added to this accommodation yet.</p>}
        <div className="space-y-2">
          {rooms?.map((room) => (
            <div key={room._id} className="border rounded-md bg-white shadow-sm">
              {/* Mobile Layout */}
              <div className="block sm:hidden p-3 space-y-3">
                <div className="flex justify-between items-start">
                  <div>
                    <span className="font-semibold text-gray-700">{room.roomNumber}</span>
                    <div className="text-sm text-gray-500 mt-1">
                      Capacity: {room.capacity}
                    </div>
                  </div>
                  <span className="text-sm font-semibold text-gray-700">GH₵{room.pricePerBedPerDay}/Bed/Day</span>
                </div>
                <div className="text-sm text-gray-500">
                  Visible to: {room.visibleTo?.join(", ") || "All"}
                </div>
                <button
                  onClick={() => {
                    setEditingRoom(room);
                    setVisibleTo(room.visibleTo || []);
                    setEditingPrice(room.pricePerBedPerDay);
                  }}
                  className="w-full text-sm bg-blue-100 text-blue-700 px-3 py-2 rounded hover:bg-blue-200 transition-colors"
                >
                  Edit Room
                </button>
              </div>

              {/* Desktop Layout */}
              <div className="hidden sm:flex justify-between items-center p-3">
                <div>
                  <span className="font-semibold text-gray-700">{room.roomNumber}</span>
                  <span className="text-sm text-gray-500 ml-4">
                    Capacity: {room.capacity}
                  </span>
                  <span className="text-sm text-gray-500 ml-4">
                    Visible to: {room.visibleTo?.join(", ") || "All"}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="font-semibold text-gray-700">GH₵{room.pricePerBedPerDay}/Bed/Day</span>
                  <button
                    onClick={() => {
                      setEditingRoom(room);
                      setVisibleTo(room.visibleTo || []);
                      setEditingPrice(room.pricePerBedPerDay);
                    }}
                    className="text-sm bg-blue-100 text-blue-700 px-3 py-1.5 rounded hover:bg-blue-200 transition-colors"
                  >
                    Edit Room
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {editingRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Edit Room</h3>
              <p className="mb-4 text-sm text-gray-600">
                Editing Room: <strong>{editingRoom.roomNumber}</strong>
              </p>

              <div className="space-y-4">
                <div>
                  <label htmlFor="editVisibleTo" className="block text-sm font-medium mb-2">
                    Visible To (Hold Ctrl/Cmd to select multiple)
                  </label>
                  <select
                    id="editVisibleTo"
                    multiple
                    value={visibleTo}
                    onChange={(e) => setVisibleTo(Array.from(e.target.selectedOptions, (option) => option.value))}
                    className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    size={4}
                  >
                    <option value="VIP">VIP</option>
                    <option value="Delegates">Delegates</option>
                    <option value="Observers">Observers</option>
                    <option value="Guests">Guests</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty to make visible to all attendee types
                  </p>
                </div>

                <div>
                  <label htmlFor="editPrice" className="block text-sm font-medium mb-2">
                    Price per Bed per Day (GH₵)
                  </label>
                  <input
                    id="editPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={editingPrice}
                    onChange={(e) => setEditingPrice(Number(e.target.value))}
                    className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter price"
                  />
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-3 mt-6">
                <button
                  onClick={() => setEditingRoom(null)}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={async () => {
                    try {
                      await handleUpdateVisibility();
                      await handleUpdatePrice();
                      setEditingRoom(null);
                    } catch (error) {
                      // Error handling is already in the individual functions
                    }
                  }}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
