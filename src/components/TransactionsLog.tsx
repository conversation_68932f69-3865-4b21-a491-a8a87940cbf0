import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { format } from "date-fns";
import { TRANSACTION_CHARGE_RATE } from "../utils/constants";
import Papa from "papaparse";
import { Download } from "lucide-react";

// import { Id } from "../../convex/_generated/dataModel";

// Placeholder: Define a type for transaction data if you have it
// interface Transaction {
//   _id: Id<"transactions">; // Example if using Convex IDs
//   date: number; // Timestamp
//   description: string;
//   amount: number;
//   currency: string;
//   status: string;
//   type: "debit" | "credit";
//   reference?: string;
//   userId?: Id<"users">;
//   eventId?: Id<"events">;
// }
import { TransactionType } from "../types";

export function TransactionsLog() {
  const transactionsData = useQuery(api.payments.getTransactions, { eventId: undefined }) as TransactionType[] | undefined;
  // Placeholder: In a real component, you'd use useQuery to fetch transactions
  // const transactions = useQuery(api.transactions.getAll); // Example Convex query

  const transactions = transactionsData
    ? [...transactionsData].sort((a, b) => b.purchaseDate - a.purchaseDate)
    : undefined;


  if (transactions === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96" aria-label="Loading transactions...">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No transactions found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Transaction Log</h1>
        <button
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          onClick={() => {
            if (transactions) {
              const csv = Papa.unparse(transactions);
              const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
              const link = document.createElement("a");
              const url = URL.createObjectURL(blob);
              link.setAttribute("href", url);
              link.setAttribute("download", "transactions.csv");
              link.style.visibility = "hidden";
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          }}
        >
          <Download className="h-4 w-4" />
          Export to CSV
        </button>
      </div>
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendee Type</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Reference</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction._id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{format(new Date(transaction.purchaseDate), "yyyy-MM-dd HH:mm:ss")}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.userEmail || "N/A"}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.eventName || "N/A"}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {transaction.transactionType === "room"
                      ? "Room Add-on"
                      : transaction.roomId
                        ? "Event + Hotel"
                        : "Event Only"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.attendeeType || "N/A"}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    GH₵{(transaction.totalPrice / (1 + TRANSACTION_CHARGE_RATE)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.paymentStatus}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.paymentReference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}