import React, { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";
import { ShieldCheckIcon } from "lucide-react";
import { EventAttendees } from "../types";

interface PaymentModalProps {
  event: any;
  quantity: number;
  selectedRoom?: {
    _id: Id<"rooms">;
    pricePerBedPerDay: number;
    capacity: number;
    type: string;
    totalPrice?: number;
  } | null;
  attendeeType?: EventAttendees;
  extraDays?: number;
  onClose: () => void;
  onSuccess: () => void;
  paymentContext?: { // New prop for context
    type: "INITIAL_REGISTRATION" | "ADD_ROOM_TO_TICKET";
    ticketId?: Id<"tickets">; // Only for ADD_ROOM_TO_TICKET
  };
}

declare global {
  interface Window {
    PaystackPop: any;
  }
}

export function PaymentModal({
  event,
  quantity,
  selectedRoom,
  attendeeType,
  extraDays = 0,
  onClose,
  onSuccess,
  paymentContext = { type: "INITIAL_REGISTRATION" }, // Default to initial registration
}: PaymentModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [paystackReady, setPaystackReady] = useState(false);
  const initializePayment = useMutation(api.payments.initializePayment);
  const verifyPayment = useMutation(api.payments.verifyPayment);
  const loggedInUser = useQuery(api.auth.loggedInUser);

  // Check if Paystack is loaded
  useEffect(() => {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    const checkPaystack = () => {
      if (window.PaystackPop) {
        console.log("✅ Paystack is available");
        setPaystackReady(true);
      } else {
        attempts++;
        if (attempts < maxAttempts) {
          console.log(`⏳ Waiting for Paystack to load... (${attempts}/${maxAttempts})`);
          setTimeout(checkPaystack, 100);
        } else {
          console.error("❌ Paystack failed to load after 5 seconds");
          console.log("🔄 Attempting to load Paystack script manually...");

          // Try to load Paystack script manually
          const script = document.createElement('script');
          script.src = 'https://js.paystack.co/v1/inline.js';
          script.onload = () => {
            console.log("✅ Paystack script loaded manually");
            if (window.PaystackPop) {
              setPaystackReady(true);
            }
          };
          script.onerror = () => {
            console.error("❌ Failed to load Paystack script manually");
            toast.error("Payment system failed to load. Please check your internet connection and try again.");
          };
          document.head.appendChild(script);
        }
      }
    };

    checkPaystack();
  }, []);

  const eventDurationInDays = React.useMemo(() => {
    if (!event.startDate || !event.endDate) return 1; // Default to 1 day if dates are not set
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 1;
  }, [event.startDate, event.endDate]);

  const ticketCost = event.price * quantity;
  const roomCostForEventDuration = selectedRoom
    ? selectedRoom.pricePerBedPerDay * eventDurationInDays
    : 0;
  const roomCostForExtraDays = selectedRoom
    ? selectedRoom.pricePerBedPerDay * selectedRoom.capacity * extraDays
    : 0;
  const roomCost = roomCostForEventDuration + roomCostForExtraDays;

  let baseAmountForTxn = 0;
  if (paymentContext.type === "ADD_ROOM_TO_TICKET") {
    baseAmountForTxn = roomCost; // Only room cost if adding to existing ticket
  } else {
    baseAmountForTxn = ticketCost + roomCost; // Event + room for initial registration
  }

  const transactionalCharge = baseAmountForTxn * 0.03; // 3% transactional charge on the relevant amount
  const totalAmount = baseAmountForTxn + transactionalCharge;
  const isExempted = loggedInUser && event.exemptedEmails?.includes(loggedInUser.email);
  const handlePayment = async () => {
    if (!loggedInUser?.email) {
      toast.error("Email is required for payment");
      return;
    }

    setIsProcessing(true);
    console.log("🚀 Starting payment process...");

    try {
      console.log("📝 Initializing payment with:", {
        eventId: event._id,
        quantity,
        email: loggedInUser.email,
        roomId: selectedRoom?._id,
        attendeeType: attendeeType,
        existingTicketId: paymentContext.type === "ADD_ROOM_TO_TICKET" ? paymentContext.ticketId : undefined,
      });

      const result = await initializePayment({
        eventId: event._id,
        quantity,
        email: loggedInUser.email,
        roomId: selectedRoom?._id,
        attendeeType: attendeeType,
        extraDays,
        existingTicketId: paymentContext.type === "ADD_ROOM_TO_TICKET" ? paymentContext.ticketId : undefined,
      } as any); // Cast as any if initializePayment args are strict and don't include existingTicketId yet

      console.log("✅ Payment initialized:", result);

      if (result.isFree) {
        toast.success("Successfully registered for the free event!");
        onSuccess();
        onClose();
        return;
      }

      // Check if Paystack is available
      if (!window.PaystackPop) {
        console.error("❌ PaystackPop is not available on window object");
        toast.error("Payment system not loaded. Please refresh the page and try again.");
        setIsProcessing(false);
        return;
      }

      console.log("💳 Setting up Paystack payment...");

      // Initialize Paystack payment
      const paystackConfig = {
        // IMPORTANT: Use your Paystack PUBLIC key here, not the secret key.
        key: (import.meta.env.VITE_PAYSTACK_PUBLIC_KEY as string) || 'pk_test_YOUR_FALLBACK_PUBLIC_KEY', // Replace with your actual public key or ensure VITE_PAYSTACK_PUBLIC_KEY is set
        email: loggedInUser.email,
        amount: (result.amount || 0) * 100, // Paystack expects amount in kobo
        currency: 'GHS', // Changed to Ghanaian Cedis
        ref: `evt_${result.ticketId}_${Date.now()}`,
        metadata: {
          ticketId: result.ticketId,
          eventId: event._id,
          quantity,
          attendeeType: attendeeType,
          roomIdToBook: result.roomIdToBook, // Pass the specific room ID for this payment
          context: paymentContext.type, // Add context to metadata
        },
        callback: function (response: any) { // Changed to a synchronous function
          console.log("✅ Paystack payment successful:", response);
          // Perform the async verification and success handling in the background
          const handleSuccessfulPayment = async () => {
            try {
              if (result && result.ticketId && response && response.reference) {
                const verifyPaymentArgs: any = {
                  ticketId: result.ticketId,
                  paymentReference: response.reference,
                  paymentStatus: "success",
                  roomIdToBook: result.roomIdToBook, // Pass the room ID to book
                };

                // Only include roomTransactionId if it exists
                if (result.roomTransactionId) {
                  verifyPaymentArgs.roomTransactionId = result.roomTransactionId;
                }

                await verifyPayment(verifyPaymentArgs);
                toast.success("Payment successful! You're registered for the event.");
                onSuccess(); // Call the onSuccess prop
                onClose();   // Close the modal
              } else {
                console.error("Paystack callback: Missing data for payment verification.", { result, response });
                toast.error("Payment verification failed due to missing data.");
              }
            } catch (error) {
              toast.error(error instanceof Error ? error.message : "Payment verification failed");
            }
          }
          handleSuccessfulPayment();
        },
        onClose: function () { // Changed to a synchronous function
          console.log("❌ Paystack payment closed/cancelled");
          setIsProcessing(false); // Re-enable UI elements immediately

          // Perform the async verification in the background
          const markAsAbandoned = async () => {
            try {
              if (result && result.ticketId) {
                const verifyPaymentArgs: any = {
                  ticketId: result.ticketId,
                  paymentReference: "", // No payment reference if abandoned
                  paymentStatus: "abandoned",
                  // No roomIdToBook needed for abandoned, as the room booking is not confirmed
                  // roomIdToBook: result.roomIdForThisPayment, // Optional: if you want to log it even for abandoned
                };

                // Only include roomTransactionId if it exists
                if (result.roomTransactionId) {
                  verifyPaymentArgs.roomTransactionId = result.roomTransactionId;
                }

                await verifyPayment(verifyPaymentArgs);
              } else {
                console.warn("Paystack onClose: ticketId not available to mark as abandoned.");
              }
            } catch (error) {
              console.error("Failed to update payment status to abandoned on close:", error);
            }
          }
          markAsAbandoned();
        },
      };

      console.log("🔧 Paystack configuration:", paystackConfig);

      const handler = window.PaystackPop.setup(paystackConfig);

      console.log("🎯 Paystack handler created:", handler);

      if (!handler || typeof handler.openIframe !== 'function') {
        console.error("❌ Invalid Paystack handler:", handler);
        toast.error("Payment system error. Please try again.");
        setIsProcessing(false);
        return;
      }

      console.log("🚀 Opening Paystack iframe...");
      handler.openIframe();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to initialize payment");
      setIsProcessing(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-bold text-gray-900">Complete Registration</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            disabled={isProcessing}
          >
            ×
          </button>
        </div>

        {!paystackReady && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
              <span className="text-yellow-800 text-sm">Loading payment system...</span>
            </div>
          </div>
        )}

        {isExempted && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <ShieldCheckIcon className="text-green-600 mr-2" />
              <span className="text-green-800 text-sm">
                You are exempted from payment for this event.
              </span>
            </div>
          </div>
        )}

        <div className="space-y-4 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">{event.title}</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <div>📅 {new Date(event.startDate).toLocaleDateString()} - {event.endDate ? new Date(event.endDate).toLocaleDateString() : 'Date TBD'}</div>
              <div>📍 {event.location}</div>
              <div>👥 {quantity} attendee(s)</div>
            </div>
            {attendeeType && (
              <div className="mt-2 text-sm text-gray-700">
                <strong>Registering as:</strong> {attendeeType}
              </div>
            )}
          </div>

          <div className="border-t pt-4">
            <div className="flex justify-between items-center mb-2">
              <span>{paymentContext.type === "ADD_ROOM_TO_TICKET" ? "Room Cost:" : "Event Cost:"}</span>
              <span>
                {paymentContext.type === "ADD_ROOM_TO_TICKET"
                  ? `GH₵${roomCost.toLocaleString()}`
                  : (event.price === 0 ? "Free" : `GH₵${ticketCost.toLocaleString()}`)}
              </span>
            </div>
            {selectedRoom && (
              <div className="mb-2 text-sm text-gray-700">
                <h4 className="font-semibold">Room Details:</h4>
                <ul className="list-disc list-inside ml-4">
                  {/* <li>Type: {selectedRoom.type}</li> */}
                  {/* <li>Price per bed per day: GH₵{selectedRoom.pricePerBedPerDay.toLocaleString()}</li> */}
                  {/* <li>Number of beds: {selectedRoom.capacity}</li> */}
                  <li>Cost for event duration: GH₵{roomCostForEventDuration.toLocaleString()}</li>
                  {extraDays > 0 && <li>Cost for extra days: GH₵{roomCostForExtraDays.toLocaleString()}</li>}
                </ul>
                <div className="flex justify-between items-center mt-2 font-medium">
                  <span>Total Room Cost:</span>
                  <span>GH₵{roomCost.toLocaleString()}</span>
                </div>
              </div>
            )}
            <div className="flex justify-between items-center mb-2">
              <span>Transactional Charges:</span>
              <span>GH₵{transactionalCharge.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
            <div className="flex justify-between items-center font-bold text-lg border-t pt-2">
              <span>Total:</span>
              <span>{totalAmount === 0 ? "Free" : `GH₵${totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}</span>
            </div>
          </div>
        </div>

        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isProcessing}
          >
            Cancel
          </button>
          <button
            onClick={handlePayment}
            disabled={isProcessing || !paystackReady}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {!paystackReady
              ? "Loading payment system..."
              : isProcessing
                ? "Processing..."
                : totalAmount === 0
                  ? (paymentContext.type === "ADD_ROOM_TO_TICKET" ? "Add Room" : "Complete Registration")
                  : (paymentContext.type === "ADD_ROOM_TO_TICKET" ? `Pay GH₵${totalAmount.toLocaleString()} for Room` : `Pay GH₵${totalAmount.toLocaleString()} & Register`)
            }
          </button>
        </div>

        {totalAmount > 0 && (
          <div className="mt-4 text-xs text-gray-500 text-center">
            <ShieldCheckIcon className="inline mr-1" />
            Secure payment
          </div>
        )}
      </div>
    </div>
  );
}
