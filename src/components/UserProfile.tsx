import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import type { UserTitle, Gender, MaritalStatus, EmploymentStatus, UserRank } from "../types/user"; // UserRank was UserProfileUpdate

// Define rank options with their display values and internal values
const rankOptions = [
  { value: "1st Lieutenant (1Lt.)", label: "1st Lieutenant (1Lt.)" },
  { value: "Noble Sister (NS)", label: "Noble Sister (NS)" },
  { value: "2nd Lieutenant (2Lt)", label: "2nd Lieutenant (2Lt)" },
  { value: "Captain (Capt.)", label: "Captain (Capt.)" },
  { value: "Major (Maj.)", label: "Major (Maj.)" },
  { value: "Lieutenant Colonel (Lt. Col)", label: "Lieutenant Colonel (Lt. Col)" },
  { value: "Colonel (Col)", label: "Colonel (Col)" },
  { value: "Brigadier General (BGen.)", label: "Brigadier General (BGen.)" },
  { value: "Major General (MGen.)", label: "Major General (MGen.)" },
  { value: "Lieutenant General (Lt. Gen.)", label: "Lieutenant General (Lt. Gen.)" },
  { value: "General", label: "General" },
] as const;
// Use UserRank from types/user if it matches, otherwise define locally or ensure consistency
// Use UserRank from types/user if it exists, otherwise fall back to the local definition
type Rank = UserRank extends string ? UserRank : typeof rankOptions[number]['value'];

// These enums should ideally be shared or generated from your Convex schema
const titles = ["Mr.", "Mrs.", "Ms", "Dr.", "Prof.", "Ing", "Hon", "Rev.", "Ps.", "Aps.", "ESQ", "Suv.", "Chief", "Nana"];
const maritalStatuses = ["Single", "Married", "Divorced", "Widowed", "Other"];
const employmentStatuses = ["Employed", "Self-Employed", "Unemployed", "Student", "Other"];
// Educational background options (matching convex/schema.ts)
const educationalBackgroundOptions = [
  "Primary",
  "Secondary",
  "Tertiary",
  "Vocational",
  "Post-Tertiary",
  "Other"
] as const;
// Grands, Districts, Commanderies would be fetched dynamically

export function UserProfile() {
  // First ensure the user exists
  const ensureUser = useMutation(api.users.ensureUserExists);
  const currentUser = useQuery(api.users.getCurrentUserWithOrgData);
  const updateUserProfile = useMutation(api.users.updateUserProfile);

  // Initialize form data with proper types
  type FormData = {
    name?: string;
    title?: UserTitle;
    gender?: Gender;
    dateOfBirth?: string;
    maritalStatus?: MaritalStatus;
    otherMaritalStatusDetails?: string;
    yearOfInitiation?: number;
    rank?: Rank;
    commanderyAuxiliaryId?: string;
    districtId?: string;
    grandId?: string;
    currentOffice?: string;
    educationalBackground?: string;
    otherEducationalBackgroundDetails?: string;
    employmentStatus?: EmploymentStatus;
    otherEmploymentStatusDetails?: string;
    careerProfession?: string;
    currentPlaceOfWork?: string;
    otherSkillsExpertise?: string;
    phoneNumber?: string;
    organizationName?: string;
  };

  const [formData, setFormData] = useState<FormData>({});
  const [isEditing, setIsEditing] = useState(false);

  // Ensure user exists when component mounts
  useEffect(() => {
    const ensureUserExists = async () => {
      try {
        await ensureUser();
      } catch (error) {
        console.error("Error ensuring user exists:", error);
      }
    };

    ensureUserExists();
  }, [ensureUser]);

  // TODO: Fetch Grands, Districts, Commanderies for dropdowns
  const grands = useQuery(api.data.listGrands);
  const districts = useQuery(api.data.listDistrictsByGrand, formData.grandId ? { grandId: formData.grandId as any } : "skip");
  const commanderies = useQuery(api.data.listCommanderiesByDistrict, formData.districtId ? { districtId: formData.districtId as any } : "skip");
  const offices = useQuery(api.data.listOffices);

  useEffect(() => {
    if (currentUser && '_creationTime' in currentUser) {
      // Create a new form data object with only the fields we want to edit
      const userFormData: FormData = {};

      if (currentUser.name) userFormData.name = currentUser.name;
      if ((currentUser as any).organizationName) userFormData.organizationName = (currentUser as any).organizationName;
      if ((currentUser as any).grandId) userFormData.grandId = (currentUser as any).grandId;
      if ((currentUser as any).districtId) userFormData.districtId = (currentUser as any).districtId;
      // Only include fields that exist on the user object and match our form data type
      if (currentUser.title) userFormData.title = currentUser.title as UserTitle;
      if (currentUser.gender) userFormData.gender = currentUser.gender as Gender;
      if (currentUser.maritalStatus) userFormData.maritalStatus = currentUser.maritalStatus as MaritalStatus;
      if (currentUser.employmentStatus) userFormData.employmentStatus = currentUser.employmentStatus as EmploymentStatus;
      if (currentUser.rank) userFormData.rank = currentUser.rank as Rank;

      // Add other fields that don't need special type handling
      const stringFields = [
        'dateOfBirth', 'otherMaritalStatusDetails', 'phoneNumber', // Changed memberNumber to phoneNumber
        'commanderyAuxiliaryId', 'currentOffice', 'educationalBackground', 'otherEducationalBackgroundDetails',
        'otherEmploymentStatusDetails', 'careerProfession', 'currentPlaceOfWork', 'otherSkillsExpertise'
      ] as const;
      stringFields.forEach(field => {
        if (field in currentUser && (currentUser as any)[field] !== undefined) {
          userFormData[field] = String((currentUser as any)[field]);
        }
      });

      // Handle number fields
      if (currentUser.yearOfInitiation !== undefined) {
        userFormData.yearOfInitiation = Number(currentUser.yearOfInitiation);
      }

      setFormData(userFormData);
    }
  }, [currentUser]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle different input types
    if (type === 'number') {
      const numValue = value === '' ? undefined : Number(value);
      setFormData(prev => ({
        ...prev,
        [name]: numValue
      } as FormData));
      return;
    }

    if (type === 'checkbox' && 'checked' in e.target) {
      setFormData(prev => ({
        ...prev,
        [name]: (e.target as HTMLInputElement).checked
      } as FormData));
      return;
    }

    if (value === '') {
      // Remove the field if value is empty
      setFormData(prev => {
        const newData = { ...prev };
        delete (newData as any)[name];
        return newData as FormData;
      });
      return;
    }

    // For text inputs and other types
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (value === '') {
      setFormData(prev => {
        const newData = { ...prev };
        delete (newData as any)[name];
        return newData as FormData;
      });
      return;
    }

    // Handle specific enum fields with proper type guards
    switch (name) {
      case 'title':
        if (titles.includes(value)) {
          setFormData(prev => ({ ...prev, title: value as UserTitle }));
        }
        break;
      case 'gender':
        if (value === 'Male' || value === 'Female') {
          setFormData(prev => ({ ...prev, gender: value as Gender }));
        }
        break;
      case 'maritalStatus':
        if (maritalStatuses.includes(value)) {
          setFormData(prev => ({ ...prev, maritalStatus: value as MaritalStatus }));
        }
        break;
      case 'employmentStatus':
        if (employmentStatuses.includes(value)) {
          setFormData(prev => ({ ...prev, employmentStatus: value as EmploymentStatus }));
        }
        break;
      case 'rank':
        if (rankOptions.some(opt => opt.value === value)) {
          setFormData(prev => ({ ...prev, rank: value as Rank }));
        }
        break;
      case 'educationalBackground':
        if (educationalBackgroundOptions.includes(value as any)) {
          setFormData(prev => ({ ...prev, educationalBackground: value }));
        }
        break;
      default:
        // For other fields
        setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser?._id) {
      toast.error("User not found");
      return;
    }

    try {
      // Create a clean updates object with only defined values
      const updates: Record<string, any> = {};

      // Only include fields that have values and are defined in FormData
      (Object.keys(formData) as Array<keyof FormData>).forEach((key) => {
        const value = formData[key];
        if (value !== undefined && value !== '') {
          // Convert empty strings to undefined for optional fields
          updates[key] = value === '' ? undefined : value;
        }
      });

      await updateUserProfile({ updates });
      toast.success("Profile updated successfully");
      setIsEditing(false);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update profile");
    }
  };

  if (!currentUser) {
    return <div className="text-center p-8">Loading profile...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900">My Profile</h2>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded"
            >
              <Pencil className="w-4 h-4 mr-2" />
              Edit Profile
            </button>
          )}
        </div>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
                Personal Information
              </h3>
              <ProfileField
                label="Full Name"
                name="name"
                value={formData.name}
                isEditing={isEditing}
                onChange={handleInputChange as any}
                currentUserValue={currentUser.name}
              />
              <ProfileField
                label="Title"
                name="title"
                value={formData.title}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={titles}
                currentUserValue={currentUser.title}
              />
              <ProfileField
                label="Gender"
                name="gender"
                value={formData.gender}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={["Male", "Female"]}
                currentUserValue={currentUser.gender}
              />
              <ProfileField
                label="Date of Birth"
                name="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                isEditing={isEditing}
                onChange={handleInputChange as any}
                currentUserValue={currentUser.dateOfBirth}
              />
              <ProfileField
                label="Marital Status"
                name="maritalStatus"
                value={formData.maritalStatus}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={maritalStatuses}
                currentUserValue={currentUser.maritalStatus}
              />
              {formData.maritalStatus === "Other" && isEditing && (
                <div className="mt-2">
                  <label htmlFor="otherMaritalStatusDetails" className="block text-sm font-medium text-gray-700">
                    Other Marital Status Details
                  </label>
                  <input
                    type="text"
                    name="otherMaritalStatusDetails"
                    value={formData.otherMaritalStatusDetails || ''}
                    onChange={handleInputChange as any}
                    className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
                  />
                </div>
              )}
            </div>

            {/* Order Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
                Order Information
              </h3>
              <ProfileField
                label="Grand"
                name="grandId"
                value={formData.grandId}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={grands?.map(g => g._id) || []}
                optionLabels={grands?.map(g => g.name) || []}
                currentUserValue={grands?.find(g => g._id === (currentUser as any).grandId)?.name}
              />
              <ProfileField
                label="District"
                name="districtId"
                value={formData.districtId}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={districts?.map(d => d._id) || []}
                optionLabels={districts?.map(d => d.name) || []}
                currentUserValue={districts?.find(d => d._id === (currentUser as any).districtId)?.name}
              />
              <ProfileField
                label="Commandery/Auxiliary"
                name="commanderyAuxiliaryId"
                value={formData.commanderyAuxiliaryId}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={commanderies?.map(c => c._id) || []}
                optionLabels={commanderies?.map(c => c.name) || []}
                currentUserValue={commanderies?.find(c => c._id === currentUser.commanderyAuxiliaryId)?.name}
              />
              <ProfileField
                label="Rank"
                name="rank"
                value={formData.rank}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={rankOptions.map(r => r.value)}
                currentUserValue={currentUser.rank}
              />
              <ProfileField
                label="Current Office"
                name="currentOffice"
                value={formData.currentOffice}
                isEditing={isEditing}
                onChange={handleSelectChange as any}
                options={offices?.map(o => o.name) || []}
                currentUserValue={currentUser.currentOffice}
              />
              <ProfileField
                label="Year of Initiation"
                name="yearOfInitiation"
                type="number"
                value={formData.yearOfInitiation?.toString()}
                isEditing={isEditing}
                onChange={handleInputChange as any}
                currentUserValue={currentUser.yearOfInitiation?.toString()}
              />
              <ProfileField
                label="Phone Number"
                name="phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                isEditing={isEditing}
                onChange={handleInputChange as any}
                currentUserValue={currentUser.phoneNumber}
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2 mt-6">
              Education & Employment
            </h3>
            <ProfileField
              label="Educational Background"
              name="educationalBackground"
              value={formData.educationalBackground}
              isEditing={isEditing}
              onChange={handleSelectChange as any}
              options={[...educationalBackgroundOptions]}
              currentUserValue={currentUser.educationalBackground}
            />
            {formData.educationalBackground === "Other" && isEditing && (
              <div className="mt-2">
                <label htmlFor="otherEducationalBackgroundDetails" className="block text-sm font-medium text-gray-700">
                  Please specify your educational background
                </label>
                <input
                  type="text"
                  name="otherEducationalBackgroundDetails"
                  value={formData.otherEducationalBackgroundDetails || ''}
                  onChange={handleInputChange as any}
                  className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
                />
              </div>
            )}
            <ProfileField
              label="Employment Status"
              name="employmentStatus"
              value={formData.employmentStatus}
              isEditing={isEditing}
              onChange={handleSelectChange as any}
              options={employmentStatuses}
              currentUserValue={currentUser.employmentStatus}
            />
            {formData.employmentStatus === "Other" && isEditing && (
              <div className="mt-2">
                <label htmlFor="otherEmploymentStatusDetails" className="block text-sm font-medium text-gray-700">
                  Other Employment Status Details
                </label>
                <input
                  type="text"
                  name="otherEmploymentStatusDetails"
                  value={formData.otherEmploymentStatusDetails || ''}
                  onChange={handleInputChange as any}
                  className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
                />
              </div>
            )}
            <ProfileField
              label="Career/Profession"
              name="careerProfession"
              value={formData.careerProfession}
              isEditing={isEditing}
              onChange={handleInputChange as any}
              currentUserValue={currentUser.careerProfession}
            />
            <ProfileField
              label="Current Place of Work"
              name="currentPlaceOfWork"
              value={formData.currentPlaceOfWork}
              isEditing={isEditing}
              onChange={handleInputChange as any}
              currentUserValue={currentUser.currentPlaceOfWork}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700 border-b pb-2 mt-6">
              Skills & Expertise
            </h3>
            <ProfileField
              label="Other Skills/Expertise"
              name="otherSkillsExpertise"
              type="textarea"
              value={formData.otherSkillsExpertise}
              isEditing={isEditing}
              onChange={handleInputChange as any}
              currentUserValue={currentUser.otherSkillsExpertise}
            />
          </div>

          {isEditing && (
            <div className="flex justify-end gap-4 pt-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </button>
              <button
                type="submit"
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}

import { Pencil, X, Save } from "lucide-react";

interface ProfileFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'number' | 'date' | 'textarea' | 'checkbox' | 'tel';
  value?: string | number | readonly string[] | undefined;
  isEditing: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  options?: string[];
  optionLabels?: string[];
  currentUserValue?: string | number | null | undefined;
}

const ProfileField: React.FC<ProfileFieldProps> = ({
  label,
  name,
  type = "text",
  value,
  isEditing,
  onChange,
  options = [],
  optionLabels = [],
  currentUserValue,
}) => {
  const displayValue = currentUserValue !== undefined && currentUserValue !== null ? String(currentUserValue) : "N/A";
  return (<div>
    <label htmlFor={name} className="block text-sm font-medium text-gray-700">
      {label}
    </label>
    {isEditing ? (
      type === "textarea" ? (
        <textarea
          name={name}
          value={value || ""}
          onChange={onChange}
          rows={3}
          className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
        /> // Check if options exist and have items before rendering a select
      ) : options && options.length > 0 ? (
        <select
          name={name}
          value={value || ""}
          onChange={onChange}
          className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
        >
          <option value="">Select {label}</option>
          {options?.map((opt: string, index: number) => (
            <option key={opt} value={opt}>
              {optionLabels[index] || opt}
            </option>
          ))}
        </select>
      ) : (
        <input
          type={type}
          name={name}
          value={value || ""}
          onChange={onChange}
          className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm"
        />
      )
    ) : (
      <p className="mt-1 text-gray-800 py-2">{displayValue}</p>
    )}
  </div>);
};

export default ProfileField;