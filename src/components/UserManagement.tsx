import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { useState } from "react";
import { EditUserProfileModal } from "./EditUserProfileModal";
import { EditIcon, Ticket, Hotel, Calendar, Eye } from "lucide-react";

// Define a type for the user data we expect from the backend
interface ManagedUser {
  _id: Id<"users">;
  name?: string | null;
  email: string;
  memberNumber?: string | null;
  organizationName?: string | null;
  isEventOrganizerGlobal?: boolean | null;
  // Add any other fields you want to display
}

interface UserTicket {
  _id: Id<"tickets">;
  eventId: Id<"events">;
  event: {
    title: string;
    startDate: number;
    location: string;
  };
  status: "confirmed" | "cancelled" | "pending";
  purchaseDate: number;
  roomId?: Id<"rooms"> | null;
  roomDetails?: {
    hotelName?: string;
    roomNumber?: string;
  } | null;
}

export function UserManagement() {
  // Fetch all users. Ensure api.users.getAllUsers is defined in your backend.
  const users = useQuery(api.users.getAllUsers) as ManagedUser[] | undefined;
  const setEventOrganizerGlobalStatus = useMutation(api.users.setEventOrganizerGlobalStatus);
  const loggedInUser = useQuery(api.users.getCurrentUser); // To prevent self-demotion if needed

  const [isLoadingMutation, setIsLoadingMutation] = useState<Record<string, boolean>>({});
  const [userToEdit, setUserToEdit] = useState<ManagedUser | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<Id<"users"> | null>(null);
  const [showUserDetails, setShowUserDetails] = useState(false);

  // Fetch user's tickets and bookings when a user is selected
  const userTickets = useQuery(
    api.tickets.getUserTickets,
    selectedUserId ? { userId: selectedUserId } : "skip"
  ) as UserTicket[] | undefined;

  const userBookings = useQuery(
    api.bookings.getUserBookings,
    selectedUserId ? { userId: selectedUserId } : "skip"
  );

  const handleSetAdminStatus = async (userId: Id<"users">, currentIsAdmin: boolean | null | undefined) => {
    if (loggedInUser?._id === userId && currentIsAdmin) {
      // Basic check to prevent self-demotion. More robust checks can be added.
      // For instance, check if they are the *only* admin.
      const confirmed = window.confirm("Are you sure you want to remove your own admin privileges? You might lose access to this page.");
      if (!confirmed) return;
    }

    setIsLoadingMutation(prev => ({ ...prev, [userId]: true }));
    try {
      const newAdminStatus = !currentIsAdmin;
      const result = await setEventOrganizerGlobalStatus({ userId, isEventOrganizerGlobal: newAdminStatus });
      toast.success(result.message || `User global organizer status updated to ${newAdminStatus}.`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update admin status.");
    } finally {
      setIsLoadingMutation(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleOpenEditModal = (user: ManagedUser) => {
    setUserToEdit(user);
  };

  const handleViewUserDetails = (userId: Id<"users">) => {
    setSelectedUserId(userId);
    setShowUserDetails(true);
  };

  const handleCloseUserDetails = () => {
    setShowUserDetails(false);
    setSelectedUserId(null);
  };

  if (users === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96" aria-label="Loading users...">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!users || users.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No users found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">User Management</h1>

      {!showUserDetails ? (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Member No.
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Organization
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Global Organizer
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {user.name || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.memberNumber || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.organizationName || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {user.isEventOrganizerGlobal ? (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Yes
                        </span>
                      ) : (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                          No
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleSetAdminStatus(user._id, user.isEventOrganizerGlobal)}
                          disabled={isLoadingMutation[user._id] || (loggedInUser?._id === user._id && !user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal && u._id !== user._id).length === 0)} // Prevent removing last admin if it's self (basic)
                          className={`px-3 py-1.5 text-xs rounded-md transition-colors
                            ${user.isEventOrganizerGlobal
                              ? 'bg-red-500 hover:bg-red-600 text-white'
                              : 'bg-green-500 hover:bg-green-600 text-white'}
                            disabled:opacity-50 disabled:cursor-not-allowed`}
                          title={loggedInUser?._id === user._id && !user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal && u._id !== user._id).length === 0 ? "Cannot remove the only global organizer if it's yourself." : (user.isEventOrganizerGlobal ? "Revoke Organizer Status" : "Grant Organizer Status")}
                        >
                          {isLoadingMutation[user._id] ? "..." : (user.isEventOrganizerGlobal ? "Revoke Organizer" : "Make Organizer")}
                        </button>
                        <button
                          onClick={() => handleOpenEditModal(user)}
                          className="text-gray-600 hover:text-gray-800 hover:underline text-xs py-1.5 px-1 rounded-md flex items-center"
                          title={`Edit ${user.name || user.email}'s profile`}
                        >
                          <EditIcon className="w-4 h-4 mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleViewUserDetails(user._id)}
                          className="text-blue-600 hover:text-blue-800 hover:underline text-xs py-1.5 px-1 rounded-md flex items-center"
                          title={`View ${user.name || user.email}'s registrations and bookings`}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Details
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {users.length === 0 && (
            <div className="text-center py-8 px-6">
              <p className="text-gray-500">No users found matching criteria.</p>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          {/* User Details View */}
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800">
                User Details: {users.find(u => u._id === selectedUserId)?.name || "User"}
              </h2>
              <button
                onClick={handleCloseUserDetails}
                className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 text-sm"
              >
                Back to Users List
              </button>
            </div>

            {/* Tickets Section */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                <Ticket className="w-5 h-5 mr-2 text-blue-600" />
                Event Registrations
              </h3>

              {userTickets === undefined ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : userTickets && userTickets.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accommodation</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {userTickets.map(ticket => (
                        <tr key={ticket._id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {ticket.event.title}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {new Date(ticket.event.startDate).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 text-xs rounded-full ${ticket.status === "confirmed" ? "bg-green-100 text-green-800" :
                                ticket.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                                  "bg-red-100 text-red-800"
                              }`}>
                              {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {new Date(ticket.purchaseDate).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {ticket.roomId ? (
                              <div className="flex items-center">
                                <Hotel className="w-4 h-4 mr-1 text-blue-600" />
                                <span>{ticket.roomDetails?.hotelName || "Hotel"} - Room {ticket.roomDetails?.roomNumber || "N/A"}</span>
                              </div>
                            ) : (
                              <span className="text-gray-400">No accommodation</span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-md">
                  <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No event registrations found for this user.</p>
                </div>
              )}
            </div>

            {/* Hotel Bookings Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                <Hotel className="w-5 h-5 mr-2 text-blue-600" />
                Hotel Bookings
              </h3>

              {userBookings === undefined ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : userBookings && userBookings.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotel</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bed</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {userBookings.map(booking => (
                        <tr key={booking._id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {booking.hotel?.name || "N/A"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.room?.roomNumber || "N/A"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.bedNumber || "Not assigned"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 text-xs rounded-full ${booking.status === "confirmed" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                              }`}>
                              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.eventTitle || "N/A"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-md">
                  <Hotel className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No hotel bookings found for this user.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {userToEdit && (
        <EditUserProfileModal
          userToEdit={userToEdit}
          onClose={() => setUserToEdit(null)}
          onSuccess={() => {
            setUserToEdit(null);
            // Users list will refetch automatically due to Convex's reactivity
            toast.info("User profile updated.");
          }}
        />
      )}
    </div>
  );
}
