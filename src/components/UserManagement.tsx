import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { useState, useCallback, useEffect, useRef, ChangeEvent, useMemo } from "react";
import { EditUserProfileModal } from "./EditUserProfileModal";
import { EditIcon, Ticket, Hotel, Calendar, Eye, Upload, X, Check, AlertCircle, Download } from "lucide-react";
import { CsvDropzone } from "./CsvDropzone";
import Papa, { ParseResult } from "papaparse";
import {
  ManagedUser,
  CsvUser,
  ImportStatus,
  CreateUsersFromCsvResponse,
  UserTicket,
  UserBooking,
  UserTitle,
  normalizeEnumValue,
  formOptions,
} from "../types/form";
import { userTitles } from "../../convex/schema";

// Custom Modal Component
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.addEventListener('keydown', handleEscape);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" onClick={onClose}>
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        <div
          ref={modalRef}
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full relative z-50" // Added relative z-50 to ensure it stacks above the backdrop
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4" id="modal-headline">
                  {title}
                </h3>
                <div className="mt-2">
                  {children}
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const nullRankValues = [
  "Not Applicable (N/A)",
  "N/A",
  "None",
  ""
];

export function UserManagement() {
  // Fetch all users. Ensure api.users.getAllUsers is defined in your backend.
  const users = useQuery(api.users.getAllUsers);
  const setEventOrganizerGlobalStatus = useMutation(api.users.setEventOrganizerGlobalStatus);
  const setEventOrganizerGlobalStatusBulk = useMutation(api.users.setEventOrganizerGlobalStatusBulk);
  const deleteUsers = useMutation(api.users.deleteUsers);
  const createUsersFromCsv = useMutation(api.users.createUsersFromCsv);
  const loggedInUser = useQuery(api.auth.loggedInUser);

  const [isLoadingMutation, setIsLoadingMutation] = useState<Record<string, boolean>>({});
  const [userToEdit, setUserToEdit] = useState<ManagedUser | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<Id<"users"> | null>(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [parsedUsers, setParsedUsers] = useState<CsvUser[]>([]);
  const [importProgress, setImportProgress] = useState(0);
  const [importStatus, setImportStatus] = useState<ImportStatus | null>(null);
  const [selectedUserIds, setSelectedUserIds] = useState<Id<"users">[]>([]);
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const userTickets = useQuery(api.tickets.getUserTickets, selectedUserId ? { userId: selectedUserId } : "skip") as UserTicket[] | undefined;
  const userBookings = useQuery(api.bookings.getUserBookings, selectedUserId ? { userId: selectedUserId } : "skip") as UserBooking[] | undefined;

  // Fetch all relational data for mapping CSV names to IDs
  const allGrands = useQuery(api.data.listGrands);
  const allDistricts = useQuery(api.data.listDistricts);
  const allCommanderiesAuxiliaries = useQuery(api.data.listCommanderiesAuxiliaries);
  const allOffices = useQuery(api.data.listOffices);

  // Create memoized maps for quick lookup (name -> ID)
  const grandNameToIdMap = useMemo(() => {
    const map = new Map<string, Id<"grands">>();
    allGrands?.forEach(g => map.set(g.name.toLowerCase(), g._id));
    return map;
  }, [allGrands]);

  const districtNameToIdMap = useMemo(() => {
    const map = new Map<string, Id<"districts">>();
    allDistricts?.forEach(d => map.set(d.name.toLowerCase(), d._id));
    return map;
  }, [allDistricts]);

  const commanderyAuxiliaryNameToIdMap = useMemo(() => {
    const map = new Map<string, Id<"commanderiesAuxiliaries">>();
    allCommanderiesAuxiliaries?.forEach(c => map.set(c.name.toLowerCase(), c._id));
    return map;
  }, [allCommanderiesAuxiliaries]);

  const officeNameToIdMap = useMemo(() => {
    const map = new Map<string, Id<"ksjiOffices">>();
    allOffices?.forEach(o => map.set(o.name.toLowerCase(), o._id));
    return map;
  }, [allOffices]);

  // Create a map for ID -> Name for display purposes
  const grandIdToNameMap = useMemo(() => {
    const map = new Map<Id<"grands">, string>();
    allGrands?.forEach(g => map.set(g._id, g.name));
    return map;
  }, [allGrands]);

  const filteredUsers = useMemo(() => {
    if (!users) return [];
    const sortedUsers = [...users].sort((a, b) => b._creationTime - a._creationTime);
    if (!searchTerm) return sortedUsers;

    const lowercasedFilter = searchTerm.toLowerCase();
    return sortedUsers.filter(user =>
      user.name?.toLowerCase().includes(lowercasedFilter) ||
      user.email?.toLowerCase().includes(lowercasedFilter) ||
      user.memberNumber?.toLowerCase().includes(lowercasedFilter)
    );
  }, [users, searchTerm]);


  const parseCsvFileForUsers = useCallback(async (file: File): Promise<CsvUser[]> => {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results: ParseResult<Record<string, string>>) => {
          if (results.errors.length > 0) {
            console.error("CSV Parsing Errors:", results.errors);
            reject(new Error('Error parsing CSV file. Check console for details.'));
            return;
          }

          const users = results.data.map((row: Record<string, any>) => {
            // Helper to get first non-empty value from a list of possible keys, case-insensitively
            const getValue = (...keys: string[]) => {
              for (const key of keys) {
                const matchingKey = Object.keys(row).find(rowKey => rowKey.trim().toLowerCase() === key.trim().toLowerCase());
                if (matchingKey && row[matchingKey]) {
                  return row[matchingKey];
                }
              }
              return undefined;
            };

            // Email
            const emailFromRow = getValue('email', 'user_email');
            let userEmail: string | undefined = undefined;
            if (emailFromRow) {
              userEmail = emailFromRow;
            } else {
              // Fallback to ensure email is found if not in primary fields
              const emailField = Object.keys(row).find(key =>
                typeof row[key] === 'string' &&
                row[key].includes('@') &&
                row[key].includes('.')
              );
              if (emailField) {
                userEmail = row[emailField];
              }
            }

            const user: Partial<CsvUser> = { email: userEmail };

            // Name
            user.name = getValue('display_name', 'display name');
            if (!user.name) {
              const firstName = getValue('First Name', 'first_name');
              const lastName = getValue('Last Name', 'last_name');
              if (firstName || lastName) {
                user.name = [firstName, lastName].filter(Boolean).join(' ').trim();
              }
            }

            // Title (normalize and map)
            const rawTitle = getValue('Title');
            const normalizedTitle = normalizeEnumValue(rawTitle, {
              addPeriodForTitles: true,
              // aliasMap: titleMap,
              trim: true
            });
            if (normalizedTitle && Object.values(userTitles).includes(normalizedTitle as UserTitle)) {
              user.title = normalizedTitle as UserTitle;
            } else {
              user.title = undefined;
            }

            // Gender
            const rawGender = getValue('Gender');
            if (rawGender && typeof rawGender === 'string') {
              if (rawGender.toLowerCase().includes('female')) user.gender = 'Female';
              else if (rawGender.toLowerCase().includes('male')) user.gender = 'Male';
            }

            // Phone Number
            const rawPhoneNumber = getValue('Mobile Number');
            if (rawPhoneNumber && typeof rawPhoneNumber === 'string') {
              const match = rawPhoneNumber.match(/tel:([+0-9\s-]+)/);
              if (match && match[1]) {
                user.phoneNumber = match[1].replace(/\s|-/g, '');
              } else {
                user.phoneNumber = rawPhoneNumber.replace(/<[^>]*>/g, '').replace(/[^0-9+]/g, '');
              }
            }

            // Other fields
            user.memberNumber = getValue('user_login');
            user.currentPlaceOfWork = getValue('Current Place of Work (Organization)');
            user.dateOfBirth = getValue('Date of Birth');
            user.maritalStatus = getValue('Marital Status');
            user.otherMaritalStatusDetails = getValue('Other Marital Status Details');

            // Professional and Educational Info
            user.employmentStatus = getValue('Employment Status');
            user.otherEmploymentStatusDetails = getValue('Other Employment Status Details');
            user.careerProfession = getValue('Career/Profession', 'Career/ Profession', 'Career / Profession');
            user.otherSkillsExpertise = getValue('Other Skills/Expertise', 'Other Skills / Expertise', 'Other Skills/Expertise');
            user.educationalBackground = getValue('Educational Background', 'Educational background/Qualification');
            user.otherEducationalBackgroundDetails = getValue('Other Educational Background Details');

            // Rank (normalize and map)
            const rawRank = getValue('Rank');
            const normalizedRank = normalizeEnumValue(rawRank, {
              removeSpacesInBrackets: true,
              nullValues: nullRankValues,
              trim: true
            });
            if (normalizedRank) {
              user.rank = normalizedRank;
            }

            // Order Information
            const rawGrand = getValue('Grand');
            if (rawGrand) {
              const grandId = grandNameToIdMap.get(`${rawGrand.toLowerCase()} grand`);

              if (grandId) {
                user.grandId = grandId;
              } else {
                console.warn(`Grand "${rawGrand}" not found in database for user ${userEmail}`);
                // Optionally, you could add this to importStatus.errors
              }
            }

            const rawDistrict = getValue('District');
            if (rawDistrict) {
              const districtId = districtNameToIdMap.get(rawDistrict.toLowerCase());
              if (districtId) {
                user.districtId = districtId;
              } else {
                console.warn(`District "${rawDistrict}" not found in database for user ${userEmail}`);
              }
            }

            const rawCommandery = getValue('Commandery/Auxiliary', 'Commandery / Auxiliary');
            if (rawCommandery) {
              const commanderyId = commanderyAuxiliaryNameToIdMap.get(rawCommandery.toLowerCase());
              if (commanderyId) {
                user.commanderyAuxiliaryId = commanderyId;
              } else {
                console.warn(`Commandery/Auxiliary "${rawCommandery}" not found in database for user ${userEmail}`);
              }
            }

            const rawCurrentOffice = getValue('Current office (if any)');
            if (rawCurrentOffice) {
              const officeId = officeNameToIdMap.get(rawCurrentOffice.toLowerCase());
              if (officeId) {
                user.officeId = officeId;
              } else {
                console.warn(`Current Office "${rawCurrentOffice}" not found in database for user ${userEmail}`);
              }
            }


            const rawYear = getValue('Year of Initiation');
            if (rawYear) {
              const date = new Date(rawYear);
              if (!isNaN(date.getTime())) {
                user.yearOfInitiation = date.getFullYear();
              }
            }

            return user;
          }).filter((user): user is CsvUser => {
            // Type guard: ensure required fields and correct types
            return (
              typeof user.email === 'string' &&
              (user.title === undefined || Object.values(userTitles).includes(user.title as UserTitle))
            );
          });
          resolve(users);
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  }, [grandNameToIdMap, districtNameToIdMap, commanderyAuxiliaryNameToIdMap, officeNameToIdMap]);

  const handleCsvFileDrop = useCallback((file: File | null) => {
    setCsvFile(file);
    setImportStatus(null);
    // setParsedUsers([]);
  }, []);

  // Function to reset all import-related states
  const resetImportStates = useCallback(() => {
    setCsvFile(null);
    setParsedUsers([]);
    setImportStatus(null);
    setImportProgress(0); // Also reset progress
  }, []);
  // Memoized function to pass to CsvDropzone, which expects a single File argument
  const memoizedParseCsvFileForUsers = useCallback(async (file: File) => {
    // Call the actual parsing function with the necessary maps
    return parseCsvFileForUsers(file);
  }, [parseCsvFileForUsers]);



  // Pagination logic
  const totalUsers = filteredUsers.length;
  const totalPages = Math.ceil(totalUsers / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstItem, indexOfLastItem);

  const handleParsedUsers = useCallback((users: CsvUser[]) => {
    setParsedUsers(users); // This is called by CsvDropzone after it successfully parses
  }, []);

  // // Effect to trigger parsing when a new CSV file is dropped
  // useEffect(() => {
  //   if (csvFile) {
  //     memoizedParseCsvFileForUsers(csvFile).then(handleParsedUsers).catch(error => toast.error(error.message));
  //   }
  // }, [csvFile, memoizedParseCsvFileForUsers, handleParsedUsers]);

  const handleImport = async () => {
    if (parsedUsers.length === 0) {
      toast.error('No valid users to import');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Process users in chunks to avoid timeouts
      const CHUNK_SIZE = 20;
      const results: CreateUsersFromCsvResponse[] = [];

      for (let i = 0; i < parsedUsers.length; i += CHUNK_SIZE) {
        const chunk = parsedUsers.slice(i, i + CHUNK_SIZE);
        const result = await createUsersFromCsv({ users: chunk });
        results.push(result);

        // Update progress
        const progress = Math.min(100, Math.round(((i + chunk.length) / parsedUsers.length) * 100));
        setImportProgress(progress);
      }

      // Aggregate results
      const aggregated: ImportStatus = {
        success: results.every(r => r.success),
        message: results.map(r => r.message).join(' '),
        created: results.reduce((sum, r) => sum + (r.created || 0), 0),
        updated: results.reduce((sum, r) => sum + (r.updated || 0), 0),
        errors: results.flatMap(r => r.errors || []),
      };

      setImportStatus(aggregated);

      if (aggregated.errors.length > 0) {
        toast.warning(`Imported ${aggregated.created} users with ${aggregated.errors.length} errors`);
      } else {
        toast.success(`Successfully imported ${aggregated.created} users`);
      }

      // Refresh user list
      // The useQuery will automatically refetch when the mutation is complete

    } catch (error) {
      console.error('Error importing users:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to import users');
    } finally {
      setIsImporting(false);
    }
  };

  const handleSetAdminStatus = async (userId: Id<"users">, currentIsAdmin: boolean | null | undefined) => {
    if (loggedInUser?._id === userId && currentIsAdmin && users?.filter(u => u.isEventOrganizerGlobal).length === 1) {
      const confirmed = window.confirm("Are you sure you want to remove your own admin privileges? You might lose access to this page.");
      if (!confirmed) return;
    }

    setIsLoadingMutation(prev => ({ ...prev, [userId]: true }));
    try {
      const newAdminStatus = !currentIsAdmin;
      const result = await setEventOrganizerGlobalStatus({
        userId,
        isEventOrganizerGlobal: newAdminStatus
      });
      toast.success(result.message || `User global organizer status updated to ${newAdminStatus}.`);
    } catch (error) {
      console.error('Error updating admin status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update admin status.');
    } finally {
      setIsLoadingMutation(prev => ({
        ...prev,
        [userId]: false
      }));
    }
  };

  const handleSelectAllGlobal = () => {
    if (filteredUsers) {
      setSelectedUserIds(filteredUsers.map(u => u._id));
    }
  };

  const handleDeselectAllGlobal = () => {
    setSelectedUserIds([]);
  };

  const handleSelectAllClick = (event: ChangeEvent<HTMLInputElement>) => {
    const currentPageIds = currentUsers.map(user => user._id);
    if (event.target.checked) {
      // Add all current page IDs to selection
      setSelectedUserIds(prev => [...new Set([...prev, ...currentPageIds])]);
    } else {
      // Remove all current page IDs from selection
      setSelectedUserIds(prev => prev.filter(id => !currentPageIds.includes(id)));
    }
  };

  // Check if all users on the current page are selected
  const areAllCurrentPageUsersSelected = currentUsers.length > 0 && currentUsers.every(user => selectedUserIds.includes(user._id));
  // Check if some users on the current page are selected but not all
  const isCurrentPageIndeterminate = currentUsers.some(user => selectedUserIds.includes(user._id)) && !areAllCurrentPageUsersSelected;
  const handleUserSelectClick = (event: ChangeEvent<HTMLInputElement>, id: Id<"users">) => {
    const isChecked = event.target.checked;
    setSelectedUserIds(prev => {
      if (isChecked) {
        return [...prev, id];
      } else {
        return prev.filter(userId => userId !== id);
      }
    });
  };

  const handleBulkDelete = async () => {
    if (selectedUserIds.length === 0 || isBulkActionLoading) return;

    const confirmed = window.confirm(`Are you sure you want to delete ${selectedUserIds.length} user(s)? This action cannot be undone.`);
    if (!confirmed) return;

    if (loggedInUser && selectedUserIds.includes(loggedInUser._id)) {
      toast.error("You cannot delete your own account via bulk actions.");
      return;
    }

    setIsBulkActionLoading(true);
    const toastId = toast.loading(`Deleting ${selectedUserIds.length} user(s)...`);

    try {
      const CHUNK_SIZE = 20;
      let totalDeleted = 0;
      const allUserIds = [...selectedUserIds]; // Create a copy to avoid state mutation issues

      for (let i = 0; i < allUserIds.length; i += CHUNK_SIZE) {
        const chunk = allUserIds.slice(i, i + CHUNK_SIZE);
        await deleteUsers({ userIds: chunk });
        totalDeleted += chunk.length;
        toast.loading(`Deleting users... (${totalDeleted}/${allUserIds.length})`, { id: toastId });
      }

      toast.success(`Successfully deleted ${totalDeleted} user(s).`, { id: toastId });
      setSelectedUserIds([]); // Clear selection
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete users.", { id: toastId });
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleBulkSetAdminStatus = async (makeAdmin: boolean) => {
    if (selectedUserIds.length === 0 || isBulkActionLoading) return;
    const action = makeAdmin ? "grant organizer status to" : "revoke organizer status from";
    if (!window.confirm(`Are you sure you want to ${action} ${selectedUserIds.length} user(s)?`)) return;

    setIsBulkActionLoading(true);
    const toastId = toast.loading(`Updating ${selectedUserIds.length} user(s)...`);

    try {
      const CHUNK_SIZE = 20;
      let totalUpdated = 0;
      const allUserIds = [...selectedUserIds];

      for (let i = 0; i < allUserIds.length; i += CHUNK_SIZE) {
        const chunk = allUserIds.slice(i, i + CHUNK_SIZE);
        await setEventOrganizerGlobalStatusBulk({ userIds: chunk, isEventOrganizerGlobal: makeAdmin });
        totalUpdated += chunk.length;
        toast.loading(`Updating users... (${totalUpdated}/${allUserIds.length})`, { id: toastId });
      }

      toast.success(`Successfully updated ${totalUpdated} user(s).`, { id: toastId });
      setSelectedUserIds([]);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update users.", { id: toastId });
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleOpenEditModal = (user: ManagedUser) => {
    setUserToEdit(user);
  };

  const handleViewUserDetails = (userId: Id<"users">) => {
    setSelectedUserId(userId);
    setShowUserDetails(true);
  };

  const handleCloseUserDetails = () => {
    setShowUserDetails(false);
    setSelectedUserId(null);
  };


  // Effect to adjust current page if it becomes invalid (e.g., after a deletion)
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
    // When users list changes (e.g. after deletion), filter out selected IDs that no longer exist
    setSelectedUserIds(prev => prev.filter(id => filteredUsers.some(u => u._id === id)));
  }, [currentPage, totalPages, filteredUsers]);

  if (users === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96" aria-label="Loading users...">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!users || users.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No users found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <div className="flex items-center gap-4">
          <button
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            onClick={() => setIsImportModalOpen(true)}
          >
            <Upload className="h-4 w-4" />
            Import Users from CSV
          </button>
          <button
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            onClick={() => {
              const csv = Papa.unparse(filteredUsers);
              const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
              const link = document.createElement("a");
              const url = URL.createObjectURL(blob);
              link.setAttribute("href", url);
              link.setAttribute("download", "users.csv");
              link.style.visibility = "hidden";
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
          >
            <Download className="h-4 w-4" />
            Export to CSV
          </button>
        </div>
      </div>

      <div className="mb-4">
        <input
          type="text"
          placeholder="Search by name, email, or member no..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setCurrentPage(1); // Reset to first page on search
          }}
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm bg-white"
        />
      </div>

      {/* Import Users Modal */}
      <Modal
        isOpen={isImportModalOpen}
        onClose={() => {
          if (!isImporting) {
            setIsImportModalOpen(false);
            resetImportStates(); // Reset states when modal is closed
          }
        }}
        title="Import Users from CSV"
      >
        <div className="space-y-4 py-4">
          <p className="text-sm text-gray-500 mb-4">
            Upload a CSV file containing user information. Required field: email.
          </p>

          <CsvDropzone
            // Specify the generic type for CsvUser
            onDrop={handleCsvFileDrop}
            currentFile={csvFile}
            parsedItemsCount={parsedUsers.length} // Renamed prop
            onParsed={handleParsedUsers}
            parseCsvFile={parseCsvFileForUsers} // Renamed prop
            label="Import Users from CSV" // Specific label for this usage
            helperText="Upload a CSV file with user data (e.g., email, name, etc.)" // Specific helper text
            inputPropsId="user-import-csv"
          />

          {importStatus && (
            <div className="space-y-2">
              <div className="flex items-center text-sm text-green-600">
                <Check className="h-4 w-4 mr-2" />
                Successfully imported {importStatus.created} users
              </div>
              {importStatus.errors.length > 0 && (
                <div className="mt-4 space-y-2">
                  <h4 className="text-sm font-medium text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {importStatus.errors.length} errors
                  </h4>
                  <div className="max-h-40 overflow-y-auto text-xs bg-gray-50 p-2 rounded border">
                    {importStatus.errors.map((error, i) => (
                      <div key={i} className="py-1">
                        <span className="font-medium">{error.email || 'N/A'}:</span> {error.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {isImporting && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Importing users...</span>
                <span>{importProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${importProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => {
                setIsImportModalOpen(false);
                resetImportStates(); // Reset states on cancel/close button click
              }}
              disabled={isImporting}
            >
              Cancel
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${!parsedUsers.length || isImporting ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={handleImport}
              disabled={!parsedUsers.length || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import Users'}
            </button>
          </div>
        </div>
      </Modal>

      {!showUserDetails ? (
        <>
          {selectedUserIds.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between">
              <span className="text-sm font-medium text-blue-800">
                {selectedUserIds.length} user(s) selected
              </span>
              <div className="flex items-center gap-2">
                <button onClick={() => handleBulkSetAdminStatus(true)} className="px-3 py-1.5 text-xs rounded-md bg-green-600 hover:bg-green-700 text-white disabled:opacity-50" disabled={isBulkActionLoading}>
                  Make Organizer
                </button>
                <button onClick={() => handleBulkSetAdminStatus(false)} className="px-3 py-1.5 text-xs rounded-md bg-yellow-600 hover:bg-yellow-700 text-white disabled:opacity-50" disabled={isBulkActionLoading}>
                  Revoke Organizer
                </button>
                <button onClick={handleSelectAllGlobal} className="px-3 py-1.5 text-xs rounded-md bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50" disabled={isBulkActionLoading}>
                  Select All ({filteredUsers.length})
                </button>
                <button onClick={handleDeselectAllGlobal} className="px-3 py-1.5 text-xs rounded-md bg-gray-600 hover:bg-gray-700 text-white disabled:opacity-50" disabled={isBulkActionLoading}>
                  Deselect All
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-1.5 text-xs rounded-md bg-red-600 hover:bg-red-700 text-white disabled:opacity-50"
                  disabled={isBulkActionLoading}
                >
                  {isBulkActionLoading ? 'Working...' : 'Delete Selected'}
                </button>
              </div>
            </div>
          )}
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <div className="flex justify-end items-center p-4">
              <label htmlFor="itemsPerPage" className="text-sm text-gray-700 mr-2">
                Items per page:
              </label>
              <select
                id="itemsPerPage"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page
                }}
                className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full min-w-full divide-y divide-gray-200 text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="p-4">
                      <div className="flex items-center">
                        <input
                          id="checkbox-all-search"
                          type="checkbox"
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          onChange={handleSelectAllClick} // Per-page select all
                          checked={areAllCurrentPageUsersSelected}
                          ref={el => {
                            if (el) { el.indeterminate = isCurrentPageIndeterminate; }
                          }}
                        />
                        <label htmlFor="checkbox-all-search" className="sr-only">checkbox</label>
                      </div>
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                      Email
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                      Member No.
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                      Grand
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                      Global Organizer
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentUsers.map((user) => (
                    <tr key={user._id} className="hover:bg-gray-50">
                      <td className="w-4 p-4">
                        <div className="flex items-center">
                          <input
                            id={`checkbox-table-search-${user._id}`}
                            type="checkbox"
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                            checked={selectedUserIds.includes(user._id)}
                            onChange={(e) => handleUserSelectClick(e, user._id)}
                          />
                          <label htmlFor={`checkbox-table-search-${user._id}`} className="sr-only">checkbox</label>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {user.name || "N/A"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.memberNumber || "N/A"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.grandId ? grandIdToNameMap.get(user.grandId) || "N/A" : "N/A"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {user.isEventOrganizerGlobal ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Yes
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            No
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === "Registered" ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"
                          }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleSetAdminStatus(user._id, user.isEventOrganizerGlobal)}
                            disabled={user.status !== 'Registered' || isLoadingMutation[user._id] || (loggedInUser?._id === user._id && user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal).length <= 1)}
                            className={`px-3 py-1.5 text-xs rounded-md transition-colors
                            ${user.isEventOrganizerGlobal
                                ? 'bg-red-500 hover:bg-red-600 text-white'
                                : 'bg-green-500 hover:bg-green-600 text-white'}
                            disabled:opacity-50 disabled:cursor-not-allowed`}
                            title={user.status !== 'Registered' ? "User must be registered to change admin status" : loggedInUser?._id === user._id && user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal).length <= 1 ? "Cannot remove the only global organizer." : (user.isEventOrganizerGlobal ? "Revoke Organizer Status" : "Grant Organizer Status")}
                          >
                            {isLoadingMutation[user._id] ? "..." : (user.isEventOrganizerGlobal ? "Revoke Organizer" : "Make Organizer")}
                          </button>
                          {/* <button
                            onClick={() => handleOpenEditModal(user as ManagedUser)}
                            disabled={user.status !== 'Registered'}
                            disabled={isLoadingMutation[user._id] || (loggedInUser?._id === user._id && user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal).length <= 1)}
                            className={`px-3 py-1.5 text-xs rounded-md transition-colors
                            ${user.isEventOrganizerGlobal
                                ? 'bg-red-500 hover:bg-red-600 text-white'
                                : 'bg-green-500 hover:bg-green-600 text-white'}
                            disabled:opacity-50 disabled:cursor-not-allowed`}
                            title={loggedInUser?._id === user._id && user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal).length <= 1 ? "Cannot remove the only global organizer." : (user.isEventOrganizerGlobal ? "Revoke Organizer Status" : "Grant Organizer Status")}
                          >
                            {isLoadingMutation[user._id] ? "..." : (user.isEventOrganizerGlobal ? "Revoke Organizer" : "Make Organizer")}
                          </button> */}
                          <button
                            onClick={() => handleOpenEditModal(user as ManagedUser)}
                            className="text-gray-600 hover:text-gray-800 hover:underline text-xs py-1.5 px-1 rounded-md flex items-center"
                            title={`Edit ${user.name || user.email}'s profile`}
                          >
                            <EditIcon className="w-4 h-4 mr-1" />
                            Edit
                          </button>
                          <button
                            onClick={() => handleViewUserDetails(user._id)}
                            className="text-blue-600 hover:text-blue-800 hover:underline text-xs py-1.5 px-1 rounded-md flex items-center"
                            title={`View ${user.name || user.email}'s registrations and bookings`}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Details
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {/* Pagination Controls */}
              <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                <div className="flex flex-1 justify-start gap-2 sm:hidden">
                  <button
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    First
                  </button>
                  <button
                    onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                </div>
                <div className="flex flex-1 justify-end gap-2 sm:hidden">
                  <button
                    onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                  <button
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Last
                  </button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing <span className="font-medium">{totalUsers > 0 ? indexOfFirstItem + 1 : 0}</span> to <span className="font-medium">{Math.min(indexOfLastItem, totalUsers)}</span> of{' '}
                      <span className="font-medium">{totalUsers}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      {/* First Page Button */}
                      <button
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">First</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M15.79 5.23a.75.75 0 01-.02 1.06L11.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02zM9.79 5.23a.75.75 0 01-.02 1.06L5.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <button
                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Previous</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" /></svg>
                      </button>
                      <span className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300">Page {currentPage} of {totalPages || 1}</span>
                      <button
                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                        disabled={currentPage === totalPages || totalPages === 0}
                        className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Next</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" /></svg>
                      </button>
                      {/* Last Page Button */}
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages || totalPages === 0}
                        className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Last</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M4.21 14.77a.75.75 0 01.02-1.06L8.168 10 4.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02zM10.21 14.77a.75.75 0 01.02-1.06L14.168 10 10.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
            {currentUsers.length === 0 && totalUsers > 0 && (
              <div className="text-center py-8 px-6">
                <p className="text-gray-500">No users on this page.</p>
              </div>
            )}
          </div>
        </>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          {/* User Details View */}
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800">
                User Details: {users?.find(u => u?._id === selectedUserId)?.name || "User"}
              </h2>
              <button
                onClick={handleCloseUserDetails}
                className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 text-sm"
              >
                Back to Users List
              </button>
            </div>

            {/* Tickets Section */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                <Ticket className="w-5 h-5 mr-2 text-blue-600" />
                Event Registrations
              </h3>

              {userTickets === undefined ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : userTickets.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accommodation</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {userTickets.map(ticket => (
                        <tr key={ticket._id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {ticket.event.title}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {new Date(ticket.event.startDate).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 text-xs rounded-full ${ticket.status === "confirmed" ? "bg-green-100 text-green-800" :
                              ticket.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                                "bg-red-100 text-red-800"
                              }`}>
                              {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {new Date(ticket.purchaseDate).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {ticket.roomId ? (
                              <div className="flex items-center">
                                <Hotel className="w-4 h-4 mr-1 text-blue-600" />
                                <span>{ticket.roomDetails?.hotelName || "Hotel"} - {ticket.roomDetails?.roomNumber || "N/A"}</span>
                              </div>
                            ) : (
                              <span className="text-gray-400">No accommodation</span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-md">
                  <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No event registrations found for this user.</p>
                </div>
              )}
            </div>

            {/* Hotel Bookings Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                <Hotel className="w-5 h-5 mr-2 text-blue-600" />
                Hotel Bookings
              </h3>

              {userBookings === undefined ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : userBookings.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotel</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bed</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {userBookings.map((booking: UserBooking) => (
                        <tr key={booking._id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {booking.hotel?.name || "N/A"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.room?.roomNumber || "N/A"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.bedNumber || "Not assigned"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 text-xs rounded-full ${booking.status === "confirmed" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                              }`}>
                              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {booking.eventTitle || "N/A"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-md">
                  <Hotel className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No hotel bookings found for this user.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {userToEdit && (
        <EditUserProfileModal
          isOpen={!!userToEdit}
          user={userToEdit}
          onClose={() => setUserToEdit(null)}
          onUpdate={() => {
            // The user list will refetch automatically. No action needed here.
          }}
        />
      )}
    </div>
  );
}
