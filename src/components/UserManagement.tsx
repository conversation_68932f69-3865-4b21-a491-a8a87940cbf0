import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { useState } from "react";
import { EditUserProfileModal } from "./EditUserProfileModal"; // Import the new modal
import { EditIcon } from "lucide-react";

// Define a type for the user data we expect from the backend
interface ManagedUser {
  _id: Id<"users">;
  name?: string | null;
  email: string;
  memberNumber?: string | null;
  organizationName?: string | null;
  isEventOrganizerGlobal?: boolean | null;
  // Add any other fields you want to display
}

export function UserManagement() {
  // Fetch all users. Ensure api.users.getAllUsers is defined in your backend.
  const users = useQuery(api.users.getAllUsers) as ManagedUser[] | undefined;
  const setEventOrganizerGlobalStatus = useMutation(api.users.setEventOrganizerGlobalStatus);
  const loggedInUser = useQuery(api.users.getCurrentUser); // To prevent self-demotion if needed

  const [isLoadingMutation, setIsLoadingMutation] = useState<Record<string, boolean>>({});
  const [userToEdit, setUserToEdit] = useState<ManagedUser | null>(null);


  const handleSetAdminStatus = async (userId: Id<"users">, currentIsAdmin: boolean | null | undefined) => {
    if (loggedInUser?._id === userId && currentIsAdmin) {
      // Basic check to prevent self-demotion. More robust checks can be added.
      // For instance, check if they are the *only* admin.
      const confirmed = window.confirm("Are you sure you want to remove your own admin privileges? You might lose access to this page.");
      if (!confirmed) return;
    }

    setIsLoadingMutation(prev => ({ ...prev, [userId]: true }));
    try {
      const newAdminStatus = !currentIsAdmin;
      const result = await setEventOrganizerGlobalStatus({ userId, isEventOrganizerGlobal: newAdminStatus });
      toast.success(result.message || `User global organizer status updated to ${newAdminStatus}.`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update admin status.");
    } finally {
      setIsLoadingMutation(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleOpenEditModal = (user: ManagedUser) => {
    setUserToEdit(user);
  };


  if (users === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96" aria-label="Loading users...">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!users || users.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No users found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">User Management</h1>
      
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Member No.
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organization
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Global Organizer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {user.name || "N/A"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.memberNumber || "N/A"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.organizationName || "N/A"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {user.isEventOrganizerGlobal ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Yes
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                        No
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleSetAdminStatus(user._id, user.isEventOrganizerGlobal)}
                      disabled={isLoadingMutation[user._id] || (loggedInUser?._id === user._id && !user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal && u._id !== user._id).length === 0) } // Prevent removing last admin if it's self (basic)
                      className={`px-3 py-1.5 text-xs rounded-md transition-colors
                        ${user.isEventOrganizerGlobal 
                          ? 'bg-red-500 hover:bg-red-600 text-white' 
                          : 'bg-green-500 hover:bg-green-600 text-white'}
                        disabled:opacity-50 disabled:cursor-not-allowed`}
                        title={loggedInUser?._id === user._id && !user.isEventOrganizerGlobal && users.filter(u => u.isEventOrganizerGlobal && u._id !== user._id).length === 0 ? "Cannot remove the only global organizer if it's yourself." : (user.isEventOrganizerGlobal ? "Revoke Organizer Status" : "Grant Organizer Status")}
                    >
                      {isLoadingMutation[user._id] ? "..." : (user.isEventOrganizerGlobal ? "Revoke Organizer" : "Make Organizer")}
                    </button>
                    <button
                      onClick={() => handleOpenEditModal(user)}
                      className="text-gray-600 hover:text-gray-800 hover:underline text-xs ml-2 py-1.5 px-1 rounded-md"
                      title={`Edit ${user.name || user.email}'s profile`}
                    >
                      <EditIcon className="w-4 h-4 mr-2" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
         {users.length === 0 && (
            <div className="text-center py-8 px-6">
                <p className="text-gray-500">No users found matching criteria.</p>
            </div>
        )}
      </div>

      {userToEdit && (
        <EditUserProfileModal
          userToEdit={userToEdit}
          onClose={() => setUserToEdit(null)}
          onSuccess={() => {
            setUserToEdit(null);
            // Users list will refetch automatically due to Convex's reactivity
            toast.info("User profile updated.");
          }}
        />
      )}
    </div>
  );
}