import { useState, useEffect, useCallback } from "react";
import { useMutation, useQuery } from "convex/react"; // useQuery might not be needed directly here unless for current user
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { EventLevel } from "@/types";
import { FileWithPath } from 'react-dropzone'; // No need for useDropzone directly if using components
import { ImageDropzone } from "./ImageDropzone";
import { CsvDropzone } from "./CsvDropzone";
import { EventFormFields } from "./EventFormFields";

const categories = [
  "All",
  "National",
  "District",
  "Grand",
  "Local",
];
const eventLevels = ["National", "Grand", "District", "Local"]; // Define if not imported
const allowedAttendeeTypes = ["VIP", "Delegates", "Observers", "Guests"]; // Define if not imported


interface EventData {
  _id: Id<"events">;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  location: string;
  price: number;
  totalTickets: number;
  category: string;
  imageUrl?: string;
  level: string;
  organizerId: Id<"users">;
  registrationCloseDate: string;
  featuredImageStorageId?: Id<"_storage">;
  allowedAttendeeTypes?: string[];
  exemptedEmails?: string[]; // Changed from exemptedMemberNumbers
}

interface EditEventModalProps {
  eventToEdit: EventData;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditEventModal({ eventToEdit, onClose, onSuccess }: EditEventModalProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    startDate: "", // Date string YYYY-MM-DD
    startTime: "", // Time string HH:MM
    endDate: "",   // Date string YYYY-MM-DD
    endTime: "",   // Time string HH:MM
    location: "",
    price: "0",
    totalTickets: "0",
    category: categories[0],
    level: eventLevels[0],
    registrationOpenDate: "", // Added for consistency, though not in EventData
    registrationCloseDate: "", // Date string YYYY-MM-DD
    imageUrl: "",
    attendees: [] as string[], // For allowed attendee types
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [exemptedEmailsFile, setExemptedEmailsFile] = useState<FileWithPath | null>(null);
  const [editableExemptedEmails, setEditableExemptedEmails] = useState<string[]>([]);
  const [featuredImageFile, setFeaturedImageFile] = useState<FileWithPath | null>(null);
  const [currentEventImageUrl, setCurrentEventImageUrl] = useState<string | null>(null);

  const updateEvent = useMutation(api.events.update);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const getFileUrl = useMutation(api.files.getUrl); // To get URL for existing storageId

  useEffect(() => {
    if (eventToEdit) {
      setFormData({
        title: eventToEdit.title,
        description: eventToEdit.description,
        startDate: eventToEdit.startDate ? new Date(Number(eventToEdit.startDate)).toISOString().split('T')[0] : "",
        startTime: eventToEdit.startTime || (eventToEdit.startDate ? new Date(Number(eventToEdit.startDate)).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false }) : ""),
        endDate: eventToEdit.endDate ? new Date(Number(eventToEdit.endDate)).toISOString().split('T')[0] : "",
        endTime: eventToEdit.endTime || (eventToEdit.endDate ? new Date(Number(eventToEdit.endDate)).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false }) : ""),
        location: eventToEdit.location,
        price: String(eventToEdit.price),
        totalTickets: String(eventToEdit.totalTickets),
        category: eventToEdit.category,
        level: eventToEdit.level,
        // registrationOpenDate: Assuming this is not part of eventToEdit, or needs to be fetched/handled
        registrationOpenDate: "", // Placeholder, adjust if eventToEdit has this
        registrationCloseDate: eventToEdit.registrationCloseDate ? new Date(Number(eventToEdit.registrationCloseDate)).toISOString().split('T')[0] : "",
        imageUrl: eventToEdit.imageUrl || "",
        attendees: eventToEdit.allowedAttendeeTypes || [],
      });
      setEditableExemptedEmails(eventToEdit.exemptedEmails || []);
      setExemptedEmailsFile(null); // Clear any previously selected file
      setFeaturedImageFile(null); // Clear any previously selected image file
      setCurrentEventImageUrl(null); // Reset before fetching

      // Fetch and set current image URL if storageId exists
      const fetchImageUrl = async () => {
        if (eventToEdit.featuredImageStorageId) {
          const url = await getFileUrl({ storageId: eventToEdit.featuredImageStorageId });
          setCurrentEventImageUrl(url);
        } else if (eventToEdit.imageUrl) {
          setCurrentEventImageUrl(eventToEdit.imageUrl); // Fallback to direct URL
        }
      };
      fetchImageUrl();
    }
  }, [eventToEdit, getFileUrl]);

  // Helper function to parse CSV
  const parseEmailsCSV = async (file: File): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const text = event.target?.result as string;
          const lines = text.split(/\r\n|\n/).filter(line => line.trim() !== '');
          const emails = lines.map(line => {
            const email = line.trim();
            if (!email.includes('@') || !email.includes('.')) { // Basic email validation
              throw new Error(`Invalid email format found: ${email}`);
            }
            return email;
          }).filter(Boolean);
          resolve(emails);
        } catch (error) {
          reject(new Error("Failed to parse CSV file."));
        }
      };
      reader.onerror = () => reject(new Error("Failed to read file."));
      reader.readAsText(file);
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!formData.title || !formData.description || !formData.startDate || !formData.startTime || !formData.endDate || !formData.endTime || !formData.location || !formData.registrationCloseDate) {
      toast.error("Please fill in all required fields");
      setIsSubmitting(false);
      return;
    }

    try {
      let featuredImageStorageIdToSave = eventToEdit.featuredImageStorageId;
      let imageUrlToSave = formData.imageUrl;

      if (featuredImageFile) {
        const postUrl = await generateUploadUrl();
        const result = await fetch(postUrl, {
          method: "POST",
          headers: { "Content-Type": featuredImageFile.type },
          body: featuredImageFile,
        });
        const { storageId } = await result.json();
        featuredImageStorageIdToSave = storageId;
        imageUrlToSave = ""; // Prioritize storageId if a new file is uploaded
      }

      await updateEvent({
        eventId: eventToEdit._id,
        title: formData.title,
        description: formData.description,
        startDate: new Date(`${formData.startDate}T${formData.startTime}`).getTime(),
        endDate: new Date(`${formData.endDate}T${formData.endTime}`).getTime(),
        startTime: formData.startTime || '00:00',
        endTime: formData.endTime || '23:59',
        location: formData.location,
        price: Number(formData.price) || 0,
        totalTickets: Number(formData.totalTickets) || 0,
        category: formData.category,
        imageUrl: formData.imageUrl || undefined,
        allowedAttendeeTypes: formData.attendees,
        featuredImageStorageId: featuredImageStorageIdToSave,
        level: (formData.level as EventLevel) || 'Local',
        registrationCloseDate: new Date(formData.registrationCloseDate).getTime().toString(),
        exemptedEmails: editableExemptedEmails, // Changed from exemptedMemberNumbers
      });

      toast.success("Event updated successfully!");
      onSuccess();
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update event");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    if (type === "checkbox" && name === "attendees") { // Ensure 'name' matches for checkboxes
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        attendees: checked
          ? [...prev.attendees, value]
          : prev.attendees.filter(item => item !== value),
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleRemoveExemptedEmail = (indexToRemove: number) => {
    setEditableExemptedEmails(prevEmails => prevEmails.filter((_, index) => index !== indexToRemove));
  };

  // Callbacks for dropzone components
  const handleFeaturedImageDrop = useCallback((file: FileWithPath | null) => {
    setFeaturedImageFile(file);
    if (file) setCurrentEventImageUrl(null); // Clear old URL preview if new file is selected
  }, []);

  const handleExemptedEmailsDrop = useCallback((file: FileWithPath | null) => {
    setExemptedEmailsFile(file);
  }, []);

  const handleExemptedEmailsParsed = useCallback((emails: string[]) => {
    setEditableExemptedEmails(emails);
  }, []);

  if (!eventToEdit) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Edit Event: {formData.title}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 text-2xl" disabled={isSubmitting}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <EventFormFields
            formData={formData}
            onFormChange={handleChange}
            featuredImageFile={featuredImageFile}
            onFeaturedImageDrop={handleFeaturedImageDrop}
            currentEventImageUrl={currentEventImageUrl}
            imageDropzoneId="edit-event-image-dropzone"
            exemptedEmailsFile={exemptedEmailsFile}
            onExemptedEmailsDrop={handleExemptedEmailsDrop}
            onExemptedEmailsParsed={handleExemptedEmailsParsed}
            parsedEmailsCount={editableExemptedEmails.length} // Use this for display in CsvDropzone
            parseEmailsCSV={parseEmailsCSV}
            csvDropzoneId="edit-event-csv-dropzone"
            isEditMode={true}
            editableExemptedEmails={editableExemptedEmails}
            onRemoveExemptedEmail={handleRemoveExemptedEmail}
            eventLevels={eventLevels}
            allowedAttendeeTypes={allowedAttendeeTypes}
            categories={categories}
          />

          <div className="flex justify-end gap-3 pt-4">
            <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Cancel</button>
            <button type="submit" disabled={isSubmitting} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
              {isSubmitting ? "Updating..." : "Update Event"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
  };