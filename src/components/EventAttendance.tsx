import { useState, useEffect } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { CheckCircle, XCircle, Loader2, Search, Calendar as CalendarIcon } from "lucide-react"; // Add startOfDay
import { format, isWithinInterval, isBefore, isAfter } from 'date-fns';
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import { toast } from "sonner";
import { Calendar } from "../components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../components/ui/popover";
import { cn } from "../lib/utils";
import { startOfDay } from 'date-fns'; // Import startOfDay

// Define the Ticket type based on the schema
type Ticket = {
  _id: Id<"tickets">;
  _creationTime: number;
  eventId: Id<"events">;
  userId: Id<"users">;
  userName: string;
  userEmail: string;
  attendeeType?: string;
  isPresent: boolean;
  status: string;
};

type Event = {
  _id: Id<"events">;
  startDate?: number;
  endDate?: number;
  title: string;
};

type EventAttendanceProps = {
  eventId: Id<"events">;
};

export function EventAttendance({ eventId }: EventAttendanceProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [calendarOpen, setCalendarOpen] = useState(false);

  // Get event details
  const event = useQuery(api.events.getById, { eventId }) as Event | undefined;

  // Get tickets with attendance for the selected date
  const formattedDate = selectedDate ? format(selectedDate, 'yyyy-MM-dd') : undefined;
  const tickets = useQuery(api.tickets.getEventTickets, { eventId, date: formattedDate });

  // Use the attendance toggle mutation
  const toggleAttendanceMutation = useMutation(api.attendance.toggleAttendance);

  // Update tickets when date changes
  useEffect(() => {
    // This will automatically refetch tickets when selectedDate changes
    // because formattedDate is a dependency of the useQuery hook
  }, [formattedDate]);

  // Generate date range for the event
  const eventDateRange = event?.startDate && event?.endDate
    ? {
      from: new Date(event.startDate),
      to: new Date(event.endDate)
    }
    : null;

  useEffect(() => {
    // Set default selected date to today if within event range, otherwise use event start date
    if (eventDateRange) {
      const today = new Date();
      if (isWithinInterval(today, { start: eventDateRange.from, end: eventDateRange.to })) {
        setSelectedDate(today);
      } else {
        setSelectedDate(eventDateRange.from);
      }
    }
  }, [event]);

  // Filter tickets based on search query
  const filteredTickets = tickets?.filter(ticket =>
    (ticket.userName?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
    (ticket.userEmail?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
    (ticket.attendeeType?.toLowerCase() || '').includes(searchQuery.toLowerCase())
  );

  const handleToggleAttendance = async (ticketId: Id<"tickets">, isPresent: boolean) => {
    if (!selectedDate) {
      toast.error("Please select a date first.");
      return;
    }
    const date = format(selectedDate, 'yyyy-MM-dd');
    try {
      await toggleAttendanceMutation({ ticketId, date, isPresent: !isPresent });
      toast.success(`Attendance updated for ${date}`);
    } catch (error) {
      toast.error("Failed to update attendance.");
      console.error(error);
    }
  };

  if (tickets === undefined) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search attendees..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal sm:w-[280px]",
                !selectedDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 bg-white shadow-lg z-50" align="start"> {/* Added bg-white, shadow, and z-index */}
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => {
                setSelectedDate(date);
                setCalendarOpen(false);
              }}
              disabled={(date) =>
                eventDateRange
                  ? isBefore(date, startOfDay(eventDateRange.from)) || isAfter(date, eventDateRange.to)
                  : false
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>List of event attendees</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-right">
                {selectedDate ? `Attendance for ${format(selectedDate, 'MMM d, yyyy')}` : 'Attendance'}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTickets?.length ? (
              filteredTickets.map((ticket) => (
                <TableRow key={ticket._id}>
                  <TableCell className="font-medium">{ticket.userName}</TableCell>
                  <TableCell>{ticket.userEmail}</TableCell>
                  <TableCell>{ticket.attendeeType || "Guest"}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant={ticket.isPresent ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleToggleAttendance(ticket._id, ticket.isPresent)}
                      disabled={!selectedDate || isAfter(startOfDay(selectedDate), startOfDay(new Date()))}
                      className={ticket.isPresent ? "bg-green-500 hover:bg-green-600 text-white" : "bg-red-500 hover:bg-red-600 text-white"}
                    >
                      {ticket.isPresent ? (
                        <CheckCircle className="mr-2 h-4 w-4" />
                      ) : (
                        <XCircle className="mr-2 h-4 w-4" />
                      )}
                      {ticket.isPresent ? "Present" : "Absent"}
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No attendees found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
