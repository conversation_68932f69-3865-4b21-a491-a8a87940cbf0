import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";

export function BookingModal({
  eventId,
  ticketId,
  onClose,
}: {
  eventId: string;
  ticketId: string;
  onClose: () => void;
}) {
  const [selectedHotelId, setSelectedHotelId] = useState<string | null>(null);
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [selectedBed, setSelectedBed] = useState<number | null>(null);

  const hotels = useQuery(api.hotels.getAccommodationsByEvent, { eventId });
  const rooms = useQuery(
    api.hotels.getRoomsByHotel,
    selectedHotelId ? { hotelId: selectedHotelId } : "skip"
  );
  const createBooking = useMutation(api.bookings.createBooking);

  const handleBooking = async () => {
    if (!selectedRoomId || !selectedBed) {
      toast.error("Please select a room and a bed.");
      return;
    }

    try {
      await createBooking({
        roomId: selectedRoomId,
        bedNumber: selectedBed,
        ticketId,
      });
      toast.success("Booking successful!");
      onClose();
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full p-6">
        <h2 className="text-2xl font-bold mb-4">Book a Room</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium">Select a Hotel</label>
            <select
              onChange={(e) => setSelectedHotelId(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option>Select accommodation</option>
              {hotels?.map((hotel) => (
                <option key={hotel._id} value={hotel._id}>
                  {hotel.name}
                </option>
              ))}
            </select>
          </div>
          {selectedHotelId && (
            <div>
              <label className="block text-sm font-medium">Select a Room</label>
              <select
                onChange={(e) => setSelectedRoomId(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option>Select a room</option>
                {rooms?.map((room) => (
                  <option key={room._id} value={room._id}>
                    {room.roomNumber} - GH₵{room.price} (Capacity: {room.capacity})
                  </option>
                ))}
              </select>
            </div>
          )}
          {selectedRoomId && (
            <div>
              <label className="block text-sm font-medium">Select a Bed</label>
              <select
                onChange={(e) => setSelectedBed(Number(e.target.value))}
                className="w-full p-2 border rounded-md"
              >
                <option>Select a bed</option>
                {Array.from(
                  { length: rooms?.find((r) => r._id === selectedRoomId)?.capacity || 0 },
                  (_, i) => i + 1
                ).map((bedNumber) => (
                  <option key={bedNumber} value={bedNumber}>
                    Bed {bedNumber}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
        <div className="mt-6 flex justify-end gap-4">
          <button
            onClick={onClose}
            className="px-4 py-2 border rounded-md"
          >
            Cancel
          </button>
          <button
            onClick={handleBooking}
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
          >
            Book Now
          </button>
        </div>
      </div>
    </div>
  );
}