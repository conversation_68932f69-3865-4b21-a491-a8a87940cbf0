import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import { EditEventModal } from "./../components/EditEventModal";
import { EventInsights } from "./EventInsights";
import MyEventCard from "./MyEventCard";
import { EventType } from "../types";
import { AdminAccommodationManagement } from "./AdminAccommodationManagement";
import { EventAttendanceModal } from "./EventAttendanceModal";

export function MyEvents() {
  const events = useQuery(api.events.myEvents);
  const [selectedEventForInsights, setSelectedEventForInsights] = useState<string | null>(null);
  const [eventToEdit, setEventToEdit] = useState<EventType | null>(null);
  const [selectedEventForHotels, setSelectedEventForHotels] = useState<string | null>(null);
  const [selectedEventForAttendance, setSelectedEventForAttendance] = useState<string | null>(null);
  const deleteEvent = useMutation(api.events.deleteEvent);

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm("Are you sure you want to delete this event? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteEvent({ eventId: eventId as any });
      toast.success("Event deleted successfully!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete event");
    }
  };

  if (events === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96" aria-label="Loading events...">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {events.map((event) => (
          <MyEventCard
            key={event._id}
            event={event as any}
            onEdit={setEventToEdit}
            onViewInsights={setSelectedEventForInsights}
            onManageAccommodations={setSelectedEventForHotels}
            onManageAttendance={setSelectedEventForAttendance}
            onDelete={handleDeleteEvent}
          />
        ))}
      </div>

      {selectedEventForHotels && (
        <div className="mt-6">
          <AdminAccommodationManagement eventId={selectedEventForHotels} />
        </div>
      )}

      {events.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">You haven't created any events yet.</p>
          <p className="text-gray-400">Click "Create Event" to get started!</p>
        </div>
      )}

      {selectedEventForInsights && (
        <EventInsights
          eventId={selectedEventForInsights}
          onClose={() => setSelectedEventForInsights(null)}
        />
      )}

      {eventToEdit && (
        <EditEventModal
          eventToEdit={eventToEdit}
          onClose={() => setEventToEdit(null)}
          onSuccess={() => {
            setEventToEdit(null);
            toast.info("Event list updated.");
          }}
        />
      )}

      {selectedEventForAttendance && (
        <EventAttendanceModal
          eventId={selectedEventForAttendance}
          isOpen={!!selectedEventForAttendance}
          onClose={() => setSelectedEventForAttendance(null)}
        />
      )}
    </div>
  );
}
