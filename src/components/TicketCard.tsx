import { Calendar, MapPin, Ticket, Banknote, XCircle } from "lucide-react";
import { TicketType } from "../types";
import { Hotel } from "lucide-react";
import { TRANSACTION_CHARGE_RATE } from "../utils/constants";

interface TicketCardProps {
  ticket: TicketType;
  onCancel: (ticketId: string) => void;
  onBookRoomRequest: (ticket: TicketType) => void; // Renamed for clarity
  hasRoomBookingForEvent?: boolean; // New prop to indicate if user has any room booking for this event
}

const TicketCard = ({ ticket, onCancel, onBookRoomRequest, hasRoomBookingForEvent }: TicketCardProps) => {
  const { event, quantity, totalPrice, purchaseDate, status } = ticket;
  const isCancelled = status === "cancelled";

  return (
    <div
      className={`bg-white rounded-lg shadow-md overflow-hidden border-l-4 ${isCancelled ? "border-red-500 opacity-75" : "border-green-500"
        }`}
    >
      <img
        src={event?.imageUrl || "https://via.placeholder.com/400x200"}
        alt={event?.title}
        className={`w-full h-32 object-cover ${isCancelled ? "grayscale" : ""}`}
      />
      <div className="p-4">
        <h4 className="text-lg font-semibold text-gray-900 mb-2">
          {event?.title}
        </h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>
              {event?.startDate
                ? new Date(event.startDate).toLocaleString()
                : "Date TBD"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            <span>{event?.location}</span>
          </div>
          <div className="flex items-center gap-2">
            <Ticket className="w-4 h-4" />
            <span>{quantity} ticket(s)</span>
          </div>
          <div className="flex items-center gap-2">
            <Banknote className="w-4 h-4" />
            <span>GH₵{(totalPrice / (1 + TRANSACTION_CHARGE_RATE)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
          </div>
        </div>
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">
              Purchased: {new Date(purchaseDate).toLocaleDateString()}
            </span>
            {!isCancelled && (
              <div className="flex gap-2">
                {ticket.roomId && ticket.roomDetails ? (
                  <div className="flex items-center text-sm text-gray-700">
                    <Hotel className="w-4 h-4 mr-1 text-blue-600" />
                    <span>Room: {ticket.roomDetails.roomNumber || ticket.roomDetails.type || 'Booked'}</span>
                  </div>
                ) : hasRoomBookingForEvent ? (
                  <div className="flex items-center text-sm text-gray-600">
                    <Hotel className="w-4 h-4 mr-1 text-gray-500" />
                    <span>Room Booked</span>
                  </div>
                ) : (
                  <button
                    onClick={() => onBookRoomRequest(ticket)}
                    className="flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    <Hotel className="w-4 h-4 mr-1" />
                    Book Room
                  </button>
                )}
                <button
                  onClick={() => onCancel(ticket._id)}
                  className="flex items-center text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  <XCircle className="w-4 h-4 mr-1" />
                  Cancel
                </button>
              </div>
            )}
            {isCancelled && (
              <span className="text-xs text-red-600 font-medium">CANCELLED</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicketCard;