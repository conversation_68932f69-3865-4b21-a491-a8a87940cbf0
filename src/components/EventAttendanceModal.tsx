import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "../components/ui/dialog";
import { EventAttendance } from "./EventAttendance";
import { Id } from "../../convex/_generated/dataModel";

type EventAttendanceModalProps = {
  eventId: Id<"events">;
  isOpen: boolean;
  onClose: () => void;
};

export function EventAttendanceModal({ eventId, isOpen, onClose }: EventAttendanceModalProps) {
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="bg-white sm:max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Manage Event Attendance</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <EventAttendance eventId={eventId} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
