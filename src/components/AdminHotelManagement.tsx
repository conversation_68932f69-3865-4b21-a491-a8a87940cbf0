import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Plus, Bed, Layers } from "lucide-react";
import { Id } from "../../convex/_generated/dataModel";

interface AdminHotelManagementProps {
  eventId?: Id<"events">;
}

export function AdminHotelManagement({ eventId }: AdminHotelManagementProps) {
  const [name, setName] = useState("");
  const [location, setLocation] = useState("");
  const [selectedHotelId, setSelectedHotelId] = useState<Id<"hotels"> | null>(null);
  const [currentManagedEventId, setCurrentManagedEventId] = useState<Id<"events"> | undefined>(eventId);

  // Fetch user's events if no specific eventId is provided, to allow selection
  const myEvents = useQuery(api.events.myEvents, !currentManagedEventId ? {} : "skip");

  const hotels = useQuery(api.hotels.getHotelsByEvent, currentManagedEventId ? { eventId: currentManagedEventId } : "skip");
  const createHotel = useMutation(api.hotels.createHotel);

  const handleCreateHotel = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentManagedEventId) {
      toast.error("Please select an event first to create a hotel for.");
      return;
    }
    try {
      await createHotel({ name, location, eventId: currentManagedEventId });
      toast.success("Hotel created successfully");
      setName("");
      setLocation("");
    } catch (error) {
      toast.error("Failed to create hotel");
    }
  };

  useEffect(() => {
    // If the eventId prop changes, update the internal state
    setCurrentManagedEventId(eventId);
  }, [eventId]);

  if (!currentManagedEventId && myEvents === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-2">Loading events...</p>
      </div>
    );
  }

  if (!currentManagedEventId && myEvents && myEvents.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <h3 className="text-lg font-semibold mb-4">Hotel Management</h3>
        <p className="text-gray-600">You have no events to manage hotels for. Please create an event first.</p>
      </div>
    );
  }

  if (!currentManagedEventId && myEvents && myEvents.length > 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Select an Event to Manage Hotels</h3>
        <div className="space-y-2">
          {myEvents.map((event) => (
            <button
              key={event._id}
              onClick={() => setCurrentManagedEventId(event._id as Id<"events">)}
              className="w-full text-left p-3 border rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {event.title}
            </button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {currentManagedEventId && (
        <>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Create New Hotel for "{myEvents?.find(e => e._id === currentManagedEventId)?.title || (eventId && 'current event')}"</h3>
            <form onSubmit={handleCreateHotel} className="flex gap-4 items-end">
              <div className="flex-1">
                <label htmlFor="hotelName" className="block text-sm font-medium">Hotel Name</label>
                <input id="hotelName" type="text" value={name} onChange={(e) => setName(e.target.value)} className="w-full p-2 border rounded-md" required />
              </div>
              <div className="flex-1">
                <label htmlFor="hotelLocation" className="block text-sm font-medium">Location</label>
                <input id="hotelLocation" type="text" value={location} onChange={(e) => setLocation(e.target.value)} className="w-full p-2 border rounded-md" required />
              </div>
              <button type="submit" className="flex items-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                <Plus className="w-4 h-4 mr-2" /> Create Hotel
              </button>
            </form>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Manage Hotels</h3>
            {hotels === undefined && <p>Loading hotels...</p>}
            {hotels && hotels.length === 0 && <p className="text-gray-500">No hotels found for this event.</p>}
            <div className="space-y-4">
              {hotels?.map((hotel) => (
                <div key={hotel._id} className="border p-4 rounded-md">
                  <h4 className="font-semibold">{hotel.name}</h4>
                  <p className="text-sm text-gray-500">{hotel.location}</p>
                  <button
                    onClick={() => setSelectedHotelId(prevId => prevId === hotel._id ? null : hotel._id)}
                    className={`text-sm mt-2 px-3 py-1 rounded ${selectedHotelId === hotel._id ? "bg-blue-500 text-white" : "bg-gray-200 text-blue-700 hover:bg-gray-300"}`}
                  >
                    {selectedHotelId === hotel._id ? "Hide Rooms" : "Manage Rooms"}
                  </button>
                  {selectedHotelId === hotel._id && (
                    <AdminRoomManagement hotelId={hotel._id} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

interface AdminRoomManagementProps {
  hotelId: Id<"hotels">;
}

function AdminRoomManagement({ hotelId }: AdminRoomManagementProps) {
  // State for single room creation
  const [roomNumber, setRoomNumber] = useState("");
  const [capacity, setCapacity] = useState(1);
  const [price, setPrice] = useState(0);

  // State for bulk room creation
  const [bulkPrefix, setBulkPrefix] = useState("Room ");
  const [bulkStartNumber, setBulkStartNumber] = useState(101);
  const [bulkNumberOfRooms, setBulkNumberOfRooms] = useState(5);
  const [bulkSuffix, setBulkSuffix] = useState("");
  const [bulkCapacity, setBulkCapacity] = useState(2);
  const [bulkPrice, setBulkPrice] = useState(100);
  const [previewRoomNames, setPreviewRoomNames] = useState<string[]>([]);
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false);

  const rooms = useQuery(api.hotels.getRoomsByHotel, { hotelId });
  const addRoom = useMutation(api.hotels.addRoom);
  const addRoomsInBulk = useMutation(api.hotels.addRoomsInBulk);

  const handleAddRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addRoom({ hotelId, roomNumber, capacity, price });
      toast.success("Room added successfully");
      setRoomNumber("");
      setCapacity(1);
      setPrice(0);
    } catch (error) {
      toast.error("Failed to add room");
    }
  };

  useEffect(() => {
    if (bulkNumberOfRooms > 0 && bulkNumberOfRooms <= 50) {
      const names = [];
      for (let i = 0; i < bulkNumberOfRooms; i++) {
        names.push(`${bulkPrefix}${bulkStartNumber + i}${bulkSuffix || ""}`);
      }
      setPreviewRoomNames(names);
    } else {
      setPreviewRoomNames([]);
    }
  }, [bulkPrefix, bulkStartNumber, bulkNumberOfRooms, bulkSuffix]);

  const handleBulkAddRooms = async (e: React.FormEvent) => {
    e.preventDefault();
    if (bulkNumberOfRooms <= 0) {
      toast.error("Number of rooms must be greater than 0.");
      return;
    }
    if (bulkNumberOfRooms > 50) {
      toast.error("Cannot create more than 50 rooms at once.");
      return;
    }
    setIsBulkSubmitting(true);
    try {
      const result = await addRoomsInBulk({
        hotelId,
        prefix: bulkPrefix,
        startNumber: bulkStartNumber,
        numberOfRooms: bulkNumberOfRooms,
        suffix: bulkSuffix,
        capacity: bulkCapacity,
        price: bulkPrice,
      });
      toast.success(result.message || `${result.count} rooms created successfully!`);
      // Reset bulk form fields
      setBulkPrefix("Room ");
      setBulkStartNumber(101);
      setBulkNumberOfRooms(5);
      setBulkSuffix("");
      setBulkCapacity(2);
      setBulkPrice(100);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to add rooms in bulk.");
    } finally {
      setIsBulkSubmitting(false);
    }
  };

  return (
    <div className="mt-4 pt-4 border-t">
      {/* Single Room Creation Form */}
      <div className="mb-8 p-4 border rounded-md bg-gray-50">
        <h5 className="font-semibold mb-3 text-md">Add Single Room</h5>
        <form onSubmit={handleAddRoom} className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
          <div className="md:col-span-2">
            <label htmlFor="roomNumber" className="block text-sm font-medium">Room Number</label>
            <input id="roomNumber" type="text" value={roomNumber} onChange={(e) => setRoomNumber(e.target.value)} className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <div>
            <label htmlFor="capacity" className="block text-sm font-medium">Capacity</label>
            <input id="capacity" type="number" value={capacity} onChange={(e) => setCapacity(Number(e.target.value))} min="1" className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <div>
            <label htmlFor="price" className="block text-sm font-medium">Price (GH₵)</label>
            <input id="price" type="number" value={price} onChange={(e) => setPrice(Number(e.target.value))} min="0" className="w-full p-2 border rounded-md mt-1" required />
          </div>
          <button type="submit" className="flex items-center justify-center bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 h-10">
            <Bed className="w-4 h-4 mr-2" /> Add Room
          </button>
        </form>
      </div>

      {/* Bulk Room Creation Form */}
      <div className="mb-8 p-4 border rounded-md bg-blue-50">
        <h5 className="font-semibold mb-3 text-md flex items-center">
          <Layers className="w-5 h-5 mr-2 text-blue-600" />
          Bulk Add Rooms (Pattern-Based)
        </h5>
        <form onSubmit={handleBulkAddRooms} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkPrefix" className="block text-sm font-medium">Prefix</label>
              <input
                id="bulkPrefix"
                type="text"
                value={bulkPrefix}
                onChange={(e) => setBulkPrefix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., Room, Suite"
              />
            </div>
            <div>
              <label htmlFor="bulkSuffix" className="block text-sm font-medium">Suffix</label>
              <input
                id="bulkSuffix"
                type="text"
                value={bulkSuffix}
                onChange={(e) => setBulkSuffix(e.target.value)}
                className="w-full p-2 border rounded-md mt-1"
                placeholder="e.g., -A, -Standard"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkStartNumber" className="block text-sm font-medium">Start Number</label>
              <input
                id="bulkStartNumber"
                type="number"
                value={bulkStartNumber}
                onChange={(e) => setBulkStartNumber(Number(e.target.value))}
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkNumberOfRooms" className="block text-sm font-medium">Number of Rooms</label>
              <input
                id="bulkNumberOfRooms"
                type="number"
                value={bulkNumberOfRooms}
                onChange={(e) => setBulkNumberOfRooms(Number(e.target.value))}
                min="1"
                max="50"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="bulkCapacity" className="block text-sm font-medium">Default Capacity (for all)</label>
              <input
                id="bulkCapacity"
                type="number"
                value={bulkCapacity}
                onChange={(e) => setBulkCapacity(Number(e.target.value))}
                min="1"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
            <div>
              <label htmlFor="bulkPrice" className="block text-sm font-medium">Default Price (GH₵ for all)</label>
              <input
                id="bulkPrice"
                type="number"
                value={bulkPrice}
                onChange={(e) => setBulkPrice(Number(e.target.value))}
                min="0"
                className="w-full p-2 border rounded-md mt-1"
                required
              />
            </div>
          </div>

          {previewRoomNames.length > 0 && (
            <div className="mt-4">
              <h6 className="text-sm font-medium mb-1">Preview of Room Names:</h6>
              <div className="p-2 border rounded-md bg-white max-h-28 overflow-y-auto text-xs">
                {previewRoomNames.slice(0, 5).map(name => <div key={name}>{name}</div>)}
                {previewRoomNames.length > 5 && <div>...and {previewRoomNames.length - 5} more.</div>}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={isBulkSubmitting}
            className="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 w-full md:w-auto disabled:opacity-50"
          >
            <Layers className="w-4 h-4 mr-2" />
            {isBulkSubmitting ? "Adding..." : "Add Rooms in Bulk"}
          </button>
        </form>
      </div>

      {/* Existing Rooms List */}
      <div>
        <h5 className="font-semibold mb-2 text-md">Existing Rooms ({rooms?.length || 0})</h5>
        {rooms === undefined && <p>Loading rooms...</p>}
        {rooms && rooms.length === 0 && <p className="text-sm text-gray-500">No rooms added to this hotel yet.</p>}
        <div className="space-y-2">
          {rooms?.map((room) => (
            <div key={room._id} className="flex justify-between items-center p-3 border rounded-md bg-white shadow-sm">
              <div>
                <span className="font-semibold text-gray-700">{room.roomNumber}</span>
                <span className="text-sm text-gray-500 ml-4">
                  Capacity: {room.capacity}
                </span>
              </div>
              <span className="font-semibold text-gray-700">GH₵{room.price}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
